import { LowDB, createOrOpenPath } from '@main/core/db'

export class TranslateDB {
  constructor({ fileName } = { fileName: 'translate.db.json' }) {
    this.init(fileName)
  }

  async init(fileName) {
    await createOrOpenPath(fileName)
    this.db = new LowDB(fileName)
    this.db.init()
  }

  // 根据 会话id 获取config
  async getTranslate({ sessionId }) {
    const res = await this.db.getOne('sessionIds', { sessionId })

    return res?.config
  }

  async setTranslate({ sessionId, config }) {
    const r = await this.db.update('sessionIds', { sessionId }, { sessionId, config })
    if (!r) {
      await this.db.create('sessionIds', { sessionId, config })
    }
  }
}
