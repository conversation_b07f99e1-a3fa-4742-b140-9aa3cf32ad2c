import { Platform } from '.'


export class TiktokHandler extends Platform {
  constructor(platform) {
    super(platform)
  }

  getUserId() {
    if (!this.userId) {
      let aEl = document.querySelector(this.sI.userIdElSelector)
      if (aEl) {
        let userId = /(?<=\/@).*(?=\?)|(?<=\/@).*/.exec(aEl.href)[0]
        if (userId) {
          this.userId = userId
        } else {
          return
        }

        let params = {
          userId: this.userId,
          platform: this.platform,
          phone: this.userId,
          nickName: this.userId,
          session_id: this.viewSessionId
        }
        this.sendPlatformUserInfo(params)
      }
    }
  }
  userId
  chatUserId

  async init(translater) {
    await super.init(translater)
    this.getUserId()
    this.bindInputFunction()
  }
  _o(mutations, observer) {
    // 绑定 input
    this.bindInputFunction()
    // 聊天页面
    this.translateList()
    // 获取未读消息的会话
    this.getUnreadSessionUserToFans()
    // 获取主账号信息
    this.getUserId()

    this.getCurrentSessionUserId()
  }

  _u(location) {
    this.getCurrentSessionUserId()
  }

  getCurrentSessionUserId() {
    if (location.pathname === '/messages') {
      let count = 0
      const func = () => {
        let currentSessionEl = document.querySelector(this.sI.currentSessionElSelector)
        if (currentSessionEl && count < 5) {
          let currentSessionUserNameEl = currentSessionEl.querySelector(this.sI.sessionUserNameElSelector)
          if (currentSessionUserNameEl && this.chatUserId !== currentSessionUserNameEl.innerText) {
            this.chatUserId = currentSessionUserNameEl.innerText
          } else {
            return
          }
          let params = {
            mainAccount: this.userId,
            fansId: this.chatUserId,
            nickName: this.chatUserId,
            platform: this.platform
          }
          this.sendCurrentSessionFansInfo(params)
        } else {
          setTimeout(() => {
            count++
            window.requestAnimationFrame(func)
          }, 500)
        }
      }
      window.requestAnimationFrame(func)
    }
  }

  getUnreadSessionUserToFans() {
    if (location.pathname === '/messages') {
      const messageItemElList = document.querySelectorAll(this.sI.sessionElSelector)

      let unreadCount = 0
      const unreadListInfo = []
      messageItemElList.forEach((sEl) => {
        const unreadEl = sEl.querySelector(this.sI.unreadElSelector)
        if (unreadEl) {
          unreadCount += 1
          if (unreadEl.hasAttribute('aira-isread')) {
            return
          } else {
            unreadEl.setAttribute('aira-isread', 'true')
            let nickName = sEl.querySelector(this.sI.sessionUserNameElSelector)?.innerText
            unreadListInfo.push({
              id: nickName,
              nickName
            })
          }
        }
      })
      if (unreadCount > 0) {
        this.sendUnReadCount(unreadCount)
      }
      if (unreadListInfo.length > 0) {
        this.sendNewFansList({
          viewSessionId: this.viewSessionId,
          platform: this.platform,
          mainAccount: this.userId,
          unreadListInfo
        })
      }
    }
  }

  bindInputFunction() {
    if (location.pathname === '/messages') {
      // console.log('message page')
      const func = (timestamp) => {
        const inputEL = document.querySelector(this.sI.inputElSelector)
        if (inputEL) {
          inputEL.addEventListener(
            'keydown',
            async (event) => {
              inputEL.setAttribute('aria-bind', 'true')
              if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
                console.log(event)
                event.stopPropagation()
                event.stopImmediatePropagation()
                this.changeInputEditStatus(false, this.sI.inputElSelector)
                let config = this.translater.config
                const isTranslate = config && config.trans_over
                const v = event.target.innerText
                if (isTranslate && v.trim()) {
                  let tV = await this.translater.translateInput(v)
                  tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, false)
                } else {
                  await this.inputMessage(v, true)
                }

                this.switchShadowState(false)
              }
            },
            {
              capture: true
            },
            true
          )

          inputEL.addEventListener('input', (event) => {
            this.createMaskDiv()
          })
        } else {
          setTimeout(() => {
            window.requestAnimationFrame(func)
          }, 500)
        }
      }
      window.requestAnimationFrame(func)
    }
  }

  sendMessageToInput({ type, message }) {
    this.inputMessage(message, type === 'send')
  }

  inputMessage(value, is_send = false) {
    this.changeInputEditStatus(true, this.sI.inputElSelector)
    return new Promise((resolve) => {
      let element = document.querySelector(this.sI.inputElSelector)
      if (element) {
        let textEl = element.querySelector(this.sI.inputTextElSelector)
        if (textEl) {
          textEl.innerText = value
          element.dispatchEvent(
            new Event('input', {
              bubbles: true,
              data: value,
              inputType: 'insertText'
            })
          )
        } else {
          const clipboardData = new DataTransfer()
          clipboardData.setData('text/plain', value)
          const pasteEvent = new ClipboardEvent('paste', {
            bubbles: true,
            cancelable: true,
            clipboardData
          })
          element.dispatchEvent(pasteEvent)
        }
        if (is_send) {
          setTimeout(() => {
            document
              .querySelector(this.sI.sendButtonElSelector)
              ?.dispatchEvent(new MouseEvent('click', { bubbles: true, view: window }))
            resolve()
          }, 50)
        }
      }
    })
  }

  switchShadowState(flag) {
    let m = document.querySelector('#myShadow')
    if (!m) {
      return
    }

    if (flag) {
      m.style.display = 'block'
    } else {
      m.style.display = 'none'
    }
  }

  createMaskDiv() {
    const maskDiv = document.querySelector('#myShadow')
    if (!maskDiv) {
      // 创建遮罩---禁止点击按钮
      let shadwoDiv = document.createElement('div')
      shadwoDiv.id = 'myShadow'
      shadwoDiv.style.position = 'absolute'
      shadwoDiv.style.width = '45px'
      shadwoDiv.style.height = '45px'
      shadwoDiv.style.top = '0px'
      shadwoDiv.style.right = '0px'
      // shadwoDiv.style.background = 'red'
      shadwoDiv.style.zIndex = 9999
      let maskContainer = document.querySelector(this.sI.sendButtonContainerElSelector)
      if (maskContainer) {
        maskContainer.style.position = 'relative'
        maskContainer.appendChild(shadwoDiv)
      } else {
        return
      }

      shadwoDiv.addEventListener('click', async (event) => {
        event.stopPropagation()
        this.changeInputEditStatus(false, this.sI.inputElSelector)
        let config = this.translater.config
        const isTranslate = config && config.trans_over
        const inputEl = document.querySelector(this.sI.inputElSelector)

        // inputEl.dispatchEvent(
        //   new KeyboardEvent('keydown', {
        //     isTrusted: true,
        //     altKey: false,
        //     bubbles: true,
        //     cancelBubble: false,
        //     cancelable: true,
        //     charCode: 0,
        //     code: 'Enter',
        //     composed: true,
        //     ctrlKey: false,
        //     currentTarget: null,
        //     defaultPrevented: false,
        //     detail: 0,
        //     eventPhase: 0,
        //     isComposing: false,
        //     key: 'Enter',
        //     keyCode: 13,
        //     location: 0,
        //     metaKey: false,
        //     repeat: false,
        //     returnValue: true,
        //     shiftKey: false
        //   })
        // )

        const v = inputEl?.innerText
        console.log(v)
        if (isTranslate && v.trim()) {
          let tV = await this.translater.translateInput(v)
          tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, false)
        } else {
          await this.inputMessage(v, true)
        }
        this.switchShadowState(false)
      })
    } else {
      this.switchShadowState(true)
    }
  }

  changeInputEditStatus(editStatus, selector) {
    try {
      let element = document.querySelector(selector)

      if (!element) return
      if (editStatus) {
        element.setAttribute('contenteditable', 'true')
      } else {
        element.setAttribute('contenteditable', 'false')
      }
    } catch (error) {
      console.error(error)
    }
  }

  async translateList() {
    let listEL = document.querySelector(this.sI.messageListElSelector)
    if (listEL) {
      const reciveMessageElList = listEL.querySelectorAll(this.sI.reciveMessageElSelector)
      reciveMessageElList.forEach((el, index) => {
        this.translater.translateMessage(el, { type: 'in' })
      })

      const sendMessageElList = listEL.querySelectorAll(this.sI.sendMessageElSelector)
      sendMessageElList.forEach((el, index) => {
        this.translater.translateMessage(el, { type: 'out' })
      })
    }
  }
}
