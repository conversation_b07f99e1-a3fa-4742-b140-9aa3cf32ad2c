import { defineStore } from 'pinia'

import { ref } from 'vue'
export const useTabStore = defineStore(
  'tabarStore',
  () => {
    const tabList = ref([])
    const nowTabbar = ref("index")
    const showHideWord = ref(true)
    const setShowHideWord = (value) => {
      showHideWord.value = value
    }

    const isOpenAi = ref("")
   
    const getCurrentSessionUserId = () => {
      let currentSessionId = ''
      tabList.value.forEach((item) => {
        if (item.session_id === nowTabbar.value) {
          currentSessionId = item.session_id
        }
        if (item.lists && item.lists.length > 0) {
          item.lists.forEach((ite) => {
            if (ite.session_id === nowTabbar.value) {
              currentSessionId = ite.session_id
            }
          })
        }
      })
      return currentSessionId
    }
    return {
      tabList,
      showHideWord,
      setShowHideWord,
      nowTabbar,
      isOpenAi,
      getCurrentSessionUserId
    }
  },
  {
    persist: true
  }
)