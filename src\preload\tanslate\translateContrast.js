export default class TranslateContrast {
  constructor(style) {
    this.setStyle(style)
    this.create()
  }
  inheritText = '翻译后内容......'

  dStyle = {
    width: '200px',
    minHeight: '30px',
    maxHeight: '300px',
    backgroundColor: '#F5F1EB',
    color: '#5f5f5f',
    display: 'none',
    alignItems: 'center',
    justifyContent: 'justify-start',
    borderRadius: '8px',
    position: 'fixed',
    overflow: 'auto',
    textDecoration: 'underline solid 2px gray',
    textUnderlineOffset: '4px',
    textIndent: '2em',
    lineHeight: '22px',
    fontSize: '16px',
    zIndex: '999999999'
  }
  d = null
  create() {
    if (this.d) return this.d

    const div = document.createElement('div')
    Object.assign(div.style, this.dStyle)
    div.textContent = this.inheritText
    document.body.append(div)

    this.d = div
    return div
  }

  show() {
    this.d.style.display = 'flex'
  }

  hide() {
    this.d.style.display = 'none'
  }

  setStyle(style) {
    if (style) {
      Object.assign(this.dStyle, style)
      this.upDStyle()
    }
  }

  setRect(style) {
    this.setStyle(style)
    this.upDStyle()
  }

  upDStyle() {
    Object.assign(this.d.style, this.dStyle)
  }

  setTextContent(t) {
    if (t) {
      this.d.textContent = t
    } else {
      this.d.textContent = this.inheritText
    }
  }
}
