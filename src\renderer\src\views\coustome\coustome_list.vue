<template>
	<div class="sys_base">
		<!-- 系统导航 -->
		<!-- <div :class="showHideWord?'sys_left':'sys_left sys_left_b'">
			<Tabar :softName='chatName' @changeChat='changeChat' @showHideWordFun='showHideWordFun'></Tabar>
		</div> -->
		<!-- 主要内容区 -->
		<div
			:class="tabarStore.showHideWord ? 'main_cont custome_list_main_cont' : 'main_cont custome_list_main_cont small_tab'">
			<!-- 会话列表 -->
			<div :class="is_open ? 'custome_list_cont open_custome_list_cont' : 'custome_list_cont'">
				<div class="chatTit">
					<div class="tit">
						<h2>客户中心</h2>
						<p>
							<span>我的默认工作空间</span>
							<!-- <img :src="icon50" alt=""> -->
						</p>
					</div>
					<div class="btns">
						<!-- <div class="item">
							<img :src="icon51" alt="">
							<p>云同步</p>
						</div>
						<div class="item">
							<img :src="icon37" alt="">
							<p>分享联系人</p>
						</div> -->
					</div>
				</div>
				<!-- 筛选信息 -->
				<div class="search_info">
					<el-form class="form" :model="searchInfo">
						<el-form-item label="应用:">
							<el-select v-model="searchInfo.fans_sources" placeholder="请选择应用">
								<el-option label="请选择应用" value=""></el-option>
								<el-option v-for="item in fansConfig.fans_sources" :label="item.name" :value="item.id"></el-option>
							</el-select>
						</el-form-item>
						<!-- <el-form-item label="来源账户:">
							<el-select v-model="searchInfo.region" placeholder="请选择应用">
								<el-option label="区域一" value="shanghai"></el-option>
								<el-option label="区域二" value="beijing"></el-option>
							</el-select>
						</el-form-item> -->
						<el-form-item label="昵称:">
							<el-input v-model="searchInfo.fans_account_name" placeholder="请输入昵称"></el-input>
						</el-form-item>
						<el-form-item label="手机号:">
							<el-input v-model="searchInfo.fans_mobile" placeholder="请输入手机号"></el-input>
						</el-form-item>
						<!-- <el-form-item label="群：">
							<el-switch v-model="searchInfo.self"></el-switch>
						</el-form-item> -->
						<div class="search_btn" @click="searchFun">
							<img :src="icon53" alt="">
							<span>查询</span>
						</div>
					</el-form>
				</div>
				<!-- 客户列表 -->
				<div class="chatList">
					<el-table empty-text="无数据" :data="tableData" tooltip-effect="dark" height="100%" style="width: 100%;">
						<template #empty>
							<div style="padding:40px 0 30px"><img :src="empty" alt="" /></div>
						</template>
						<el-table-column prop="fans_account_name" label="昵称" width="120">
						</el-table-column>
						<el-table-column prop="fans_mobile" label="电话" width="120">
						</el-table-column>
						<el-table-column prop="company" label="公司" width="120">
						</el-table-column>
						<el-table-column prop="position" label="职位" width="120">
						</el-table-column>
						<el-table-column prop="fans_level_text" label="销售信息" width="260">
						</el-table-column>
						<el-table-column prop="country" label="国家" show-overflow-tooltip>
						</el-table-column>
						<el-table-column prop="order_stage_text" label="标签" width="120">
							<!-- <template #default="scope">
								<div class="tags">
									<p>已预约</p>
								</div>
							</template> -->
						</el-table-column>
						<el-table-column prop="fans_sources_text" label="来源" width="120">
						</el-table-column>
						<el-table-column prop="name" label="操作" width="150">
							<template #default="scope">
								<el-button class="setBtn" @click="editCustomeInfo(scope.row)" type="text" size="small">编辑</el-button>
								<!-- <el-button class="setBtn del" type="text" size="small">删除</el-button> -->
							</template>
						</el-table-column>
					</el-table>
				</div>
				<!-- 分页设置 -->
				<!-- <div class="pageInfo">
					<el-pagination
					  background
					  :page-size='15'
					  layout="prev, pager, next"
					  :total="1000">
					</el-pagination>
				</div> -->

				<!-- <div class="custome_empty" v-if="tableData.length==0">
					<div class="empty">
						<img :src="icon52" alt="">
						<p>暂无内容</p>
					</div>
				</div> -->
			</div>
			<!-- 用户设置 -->
			<div :class="is_open ? 'soft_set open_soft_set' : 'soft_set'">
				<UserSet ref="UserSetRef" @openSetPage='openSetPage' :softName='chatName'></UserSet>
			</div>
			<!-- 弹框设置 -->
			<div class="shadow" v-if="is_show_flag">
				<div class="userinfo_flag">
					<div class="flag_tit">
						<h2>用户信息</h2>
						<img @click="closeFlag" :src="icon54" alt="">
					</div>
					<div class="flag_tag">
						<div :class="infoType == 1 ? 'item on' : 'item'" @click="changeFlagType(1)">基本信息</div>
						<div :class="infoType == 2 ? 'item on' : 'item'" @click="changeFlagType(2)">备注</div>
					</div>
					<div class="flag_form">
						<el-form class="forms" :model="fansUpdate">
							<div class="flag_item" v-if="infoType == 1">
								<el-form-item label="备注名">
									<el-input v-model="fansUpdate.fans_name" placeholder="请输入备注名"></el-input>
								</el-form-item>
								<el-form-item label="电话">
									<el-input v-model="fansUpdate.fans_mobile" placeholder="请输入电话"></el-input>
								</el-form-item>
								<el-form-item label="国家">
									<el-input v-model="fansUpdate.country" placeholder="请输入国家"></el-input>
								</el-form-item>
								<el-form-item label="性别">
									<div class="sex_info">
										<div :class="sex == 1 ? 'item on' : 'item'" @click="cahngeSex(1)">
											<p>
												<img :src="icon44" alt="">
											</p>
											<span>男</span>

										</div>
										<div :class="sex == 2 ? 'item on' : 'item'" @click="cahngeSex(2)">
											<p>
												<img :src="icon44" alt="">
											</p>
											<span>女</span>

										</div>
									</div>
								</el-form-item>
							</div>
							<h3 v-if="infoType == 1"><span>销售信息</span></h3>
							<div class="flag_item sale" v-if="infoType == 1">
								<el-form-item label="客户等级">
									<el-select v-model="fansUpdate.fans_level" placeholder="请选择客户等级">
										<el-option label="请选择客户等级" :value="0"></el-option>
										<el-option v-for="item in fansConfig.fans_level" :label="item.name" :value="item.id"></el-option>
									</el-select>
								</el-form-item>
								<el-form-item label="订单阶段">
									<el-select v-model="fansUpdate.order_stage" placeholder="请选择订单阶段">
										<el-option label="请选择订单阶段" :value="0"></el-option>
										<el-option v-for="item in fansConfig.order_stage" :label="item.name" :value="item.id"></el-option>
									</el-select>
								</el-form-item>
								<el-form-item label="销售阶段">
									<el-select v-model="fansUpdate.sales_stage" placeholder="请选择销售阶段">
										<el-option label="请选择销售阶段" :value="0"></el-option>
										<el-option v-for="item in fansConfig.sales_stage" :label="item.name" :value="item.id"></el-option>
									</el-select>
								</el-form-item>
								<el-form-item label="社媒来源">
									<el-select v-model="fansUpdate.fans_sources" placeholder="请选择社媒来源">
										<el-option label="请选择媒体" :value="0"></el-option>
										<el-option v-for="item in fansConfig.fans_sources" :label="item.name" :value="item.id"></el-option>
									</el-select>
								</el-form-item>

								<!-- <el-form-item label="自定义标签">
									<el-select v-model="fansUpdate.region" placeholder="请输入标签">
										<el-option label="区域一" value="shanghai"></el-option>
										<el-option label="区域二" value="beijing"></el-option>
									</el-select>
								</el-form-item> -->
							</div>
							<h3 v-if="infoType == 1"><span>公司信息</span></h3>
							<div class="flag_item conpany" v-if="infoType == 1">

								<el-form-item label="公司名称">
									<el-input v-model="fansUpdate.company" placeholder="请输入公司名称"></el-input>
								</el-form-item>
								<el-form-item label="职位">
									<el-input v-model="fansUpdate.position" placeholder="请输入职位"></el-input>
								</el-form-item>
							</div>

							<div class="flag_remark" v-if="infoType == 2">
								<div class="remarks">{{ fansUpdate.remark }}</div>
								<div class="input_remark">
									<el-input placeholder="请输入新的备注" type="textarea" v-model="fansUpdate.remark"></el-input>
								</div>
							</div>

							<div class="btns">
								<div class="item" @click="closeFlag">关闭</div>
								<div class="item" @click="updateFansInfo">保存</div>
							</div>
						</el-form>
					</div>
				</div>
			</div>

		</div>
	</div>

</template>

<script setup>
import { useTabStore } from "@renderer/store/index.js"
const tabarStore = useTabStore()
// import SoftSet from '../../components/SoftSet.vue'
import UserSet from '../../components/UserSet.vue'
import {
	ref, watch,
	onMounted,
	onUnmounted
} from 'vue'
import {
	getGustListSql, getFansConfigSql, updateFansSql,
} from "../../api/account.js"
import { ElMessage } from 'element-plus'
// import {
// 	getWorkOrderSql
// } from "../../api/account.js"
import { useRouter, useRoute } from 'vue-router'  // 导入 useRouter
const router = useRouter()  // 获取路由实例
const route = useRoute();  // 获取当前路由对象
// 导入本地图片
const icon50 = new URL('../../assets/img/icon50.svg', import.meta.url).href;
const icon37 = new URL('../../assets/img/icon37.svg', import.meta.url).href;
const icon51 = new URL('../../assets/img/icon51.svg', import.meta.url).href;
const icon52 = new URL('../../assets/img/icon52.svg', import.meta.url).href;
const icon44 = new URL('../../assets/img/icon44.png', import.meta.url).href;
const icon53 = new URL('../../assets/img/icon53.svg', import.meta.url).href;
const icon54 = new URL('../../assets/img/icon54.png', import.meta.url).href;


const showHideWord = ref(true)
watch(
	() => route.query,
	(newParams, oldParams) => {
		// 设置左侧是否显示
		if (newParams.chat == 'slide') {
			showHideWord.value = newParams.type == 'true' ? true : false
		}
	},
	{ immediate: true } // 立即执行一次，以确保初始参数也被捕获
);
// 删选数据translateSet
const searchInfo = ref({})
// 表格数据
const tableData = ref([])
function getGustList(orderNumber) {

	getGustListSql(searchInfo.value).then(response => {
		if (response.code == 1) {
			// ElMessage({
			// 	showClose: true,
			// 	message: '操作成功',
			// 	type: 'success'
			// })
			tableData.value = response.data.data
		}
	})
}
getGustList()
const searchFun = () => {
	getGustList()
}
// 更新粉丝信息
const fansUpdate = ref({ fansSex: 1 })
function updateFansInfo() {
	let fromData = {
		'fans_id': fansUpdate.value.id,
		'is_customer': 1,
		'fans_name': fansUpdate.value.fans_name,
		'fans_sex': fansUpdate.value.fans_sex,
		'fans_mobile': fansUpdate.value.fans_mobile,
		'country': fansUpdate.value.country,
		'fans_level': fansUpdate.value.fans_level,
		'order_stage': fansUpdate.value.order_stage,
		'sales_stage': fansUpdate.value.sales_stage,
		'fans_sources': fansUpdate.value.fans_sources,
		'company': fansUpdate.value.company,
		'position': fansUpdate.value.position,
		'remark': fansUpdate.value.remark,
	}
	updateFansSql(fromData).then(response => {
		if (response.code == 1) {
			ElMessage({
				showClose: true,
				message: '操作成功',
				type: 'success'
			})
			is_show_flag.value = false
			getGustList()
		} else {
			ElMessage({
				showClose: true,
				message: '操作失败',
				type: 'info'
			})

		}
	})
}
// 设置更新弹框
const formKey = ref(0)
const editCustomeInfo = (item) => {
	is_show_flag.value = true
	item.fans_level = Number(item.fans_level)
	item.order_stage = Number(item.order_stage)
	item.sales_stage = Number(item.sales_stage)
	item.fans_sources = Number(item.fans_sources)

	item.fans_name = item.fans_account_name
	fansUpdate.value = item
	sex.value = item.fans_sex
	formKey.value = formKey.value + 1


}
// 获取粉丝配置
const fansConfig = ref({})
function getFansConfig() {
	getFansConfigSql({}).then(response => {
		if (response.code == 1) {
			fansConfig.value = response.data
		}
	})
}
getFansConfig()
// 支持平台列表
const chatName = ref()
onMounted(() => {
	chatName.value = route.query.chat
})
// 改变平台
const changeChat = (cN) => {
	chatName.value = cN
}
// 设置是否显示设置信息页面
const is_open = ref(false)
const openSetPage = (son_b) => {
	is_open.value = son_b
}
// 弹框tab切换=====================
const infoType = ref(1)
const changeFlagType = (type) => {
	infoType.value = type
}
// 弹框 性别设置
const sex = ref(1)
const cahngeSex = (type) => {
	fansUpdate.fansSex = type
	sex.value = type
}
// 关闭弹框
const is_show_flag = ref(false)
const closeFlag = () => {
	is_show_flag.value = false
}
</script>

<style lang="scss">
@import url("../../assets/style/coustome_list.scss");

.el-popper.is-pure {
	z-index: 99999999 !important;
}

.el-table__empty-block {
	width: auto !important;
	height: auto !important;
}
</style>