import { preloadJsPathMap } from '../index.js'
import { GlobalObject } from '@main/global/index.js'
import electron, { app, dialog, session } from 'electron'
import loadingCssString from '@main/assets/loading.css?raw'
import contextMenuCssString from '@main/assets/contextMenu.css?raw'
import { getUs } from "@main/utils/index.js"
import { platform } from '@electron-toolkit/utils'
export class ViewManager {
  constructor() {
    this.viewsMap = /* @__PURE__ */ new Map()
  }
  viewsMap
  setViewBounds(view, { x, y, width, height }) {
    // 添加边界检查，防止 macOS 下的崩溃
    const safeX = Math.max(1, Math.floor(x || 0))
    const safeY = Math.max(1, Math.floor(y || 0))
    const safeWidth = Math.max(1, Math.floor(width || 0)) // 最小宽度 1px
    const safeHeight = Math.max(1, Math.floor(height || 0)) // 最小高度 1px

    view.setBounds({
      x: safeX,
      y: safeY,
      width: safeWidth,
      height: safeHeight
    })
  }
  addView(viewConfig) {
    try {
      let view
      if (!this.viewsMap.has(viewConfig.name)) {
        const s = session.fromPartition(`${viewConfig.itemInfo.session_id}`, {
          cache: true
        })
        console.log('s.storagePath', s.storagePath)

        s.webRequest.onBeforeSendHeaders((details, callback) => {
          callback({
            requestHeaders: {
              ...details.requestHeaders,
              'User-Agent': getUs(platform.isMacOS)
            }
          })
        })
        s.setPermissionRequestHandler((webContents, permission, callback) => {
          callback(true)
        })
        view = new ViewInfo({
          ...viewConfig,
          webPreferences: {
            sandbox: false,
            nodeIntegration: false,
            contextIsolation: ['whatsapp', 'facebook'].includes(viewConfig.itemInfo.platform)
              ? false
              : true,
            media: true,
            transparent: true,
            webSecurity: true,
            preload: preloadJsPathMap.get(viewConfig.platform),
            session: s
          }
        })
        ViewHandler.init(view)

        if (platform.isMacOS) {
          view.webContents.once('did-finish-load', () => {
            this.viewsMap.set(viewConfig.name, view)
            if (!viewConfig.noAttach) {
              this.attachViewToWindow(view)
            }
          })
        } else if (platform.isWindows) {
          this.viewsMap.set(viewConfig.name, view)
          if (!viewConfig.noAttach) {
            this.attachViewToWindow(view)
          }
        } else {
          this.viewsMap.set(viewConfig.name, view)
          if (!viewConfig.noAttach) {
            this.attachViewToWindow(view)
          }
        }
        if (!app.isPackaged || import.meta.env.MODE === 'test') {
          view.webContents.openDevTools()
        }
      } else {
        let view = this.viewsMap.get(viewConfig.name)
        GlobalObject.mainWindow.contentView.addChildView(view)
      }

      view.webContents
        .loadURL(viewConfig.url, { userAgent: getUs(platform.isMacOS) })
        .catch((err) => {
          console.log(err, 'loadurl error')
          return err
        })
      return true
    } catch (error) {
      console.log(error)
      return false
    }
  }

  attachViewToWindow(view) {
    this.setViewBounds(view, GlobalObject.viewBounds)
    GlobalObject.mainWindow.contentView.addChildView(view)
  }

  getView(name) {
    return this.viewsMap.get(name)
  }
  deleteView(name) {
    return this.viewsMap.delete(name)
  }
}
class ViewInfo extends electron.WebContentsView {
  constructor(viewConfig) {
    super(viewConfig)
    this.name = viewConfig.name
    this.url = viewConfig.url
    this.itemInfo = viewConfig.itemInfo
  }
}

export function onMainBwReisze(bw) {
  bw.on('resize', () => {
    if (GlobalObject.viewManager?.viewsMap) {
      GlobalObject.viewManager.viewsMap.forEach((view) => {
        if (view && GlobalObject.viewBounds) {
          GlobalObject.viewManager.setViewBounds(view, GlobalObject.viewBounds)
        }
      })
    }
  })
}

class ViewHandler {
  static init(view) {
    this.setUserAgent(view)
    this.setViewOpenHandler(view)
    this.insertCss(view)
  }

  static interceptUrlRegExpMap = new Map([
    ['facebook', [new RegExp('https://business.facebook.com/')]]
  ])

  static setViewOpenHandler(view) {
    const handler = (e) => {
      console.log(e)
      this.interceptUrlRegExpMap.forEach((v, k) => {
        if (k === view.itemInfo.platform) {
          v.forEach((r) => {
            if (r.test(e.url)) {
              e.preventDefault()
              dialog.showMessageBox({
                title: '提示',
                message: `当前url已被拦截:\n ${r.toString().split('\\').join('')}`
              })
            }
          })
        }
      })
    }
    view.webContents.on('will-navigate', handler)
    view.webContents.on('new-window', handler)
  }

  static setUserAgent(view) {
    view.webContents.setUserAgent(getUs(platform.isMacOS))
  }

  static insertCss(view) {
    view.webContents.on('dom-ready', () => {
      view.webContents.insertCSS(loadingCssString)
      view.webContents.insertCSS(contextMenuCssString)
      console.log('view load success')
    })
  }
}
