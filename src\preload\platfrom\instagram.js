import { Platform } from '.'


export class InstagramHandler extends Platform {
  constructor(platform) {
    super(platform)
  }
  idRegular = /(?<=\/t\/)[\d]{1,}(?=\/?)|$/
  userId = undefined
  userName = undefined
  prefilePicture = undefined
  chatUserId = undefined
  isBindInputFunction = false

  async init(translater) {
    await super.init(translater)
    this.bindInputFunction()
    this.getCurrentSessionUserId()
  }
  _o(mutations, observer) {
    // 绑定 input
    this.bindInputFunction()
    // 聊天页面
    this.translateList()
    // 获取未读消息的会话
    this.getUnreadSessionUserToFans()
    // 获取主账号信息
    this.getUserInfo()
  }

  _u(location) {
    this.getCurrentSessionUserId()
  }

  getCurrentSessionUserId() {
    if (location.pathname.includes('/direct/')) {
      const func = () => {
        let c = document.querySelector(this.sI.sessionCurrentUserElSelector)
        let nickName = ''
        if (c) {
          nickName = c.querySelector(this.sI.sessionUserNickNameElSelector)?.innerText
        }

        if (nickName && nickName !== this.chatUserId) {
          if (nickName == this.userId) {
            return
          }
          this.chatUserId = nickName
          let params = {
            mainAccount: this.userId,
            fansId: nickName,
            nickName: nickName,
            platform: this.platform
          }
          this.sendCurrentSessionFansInfo(params)
        } else {
          setTimeout(() => {
            window.requestAnimationFrame(func)
          }, 500)
        }
      }
      window.requestAnimationFrame(func)
    }
  }

  getUnreadSessionUserToFans() {
    if (location.pathname.includes('/direct/')) {
      const listEl = document.querySelector(this.sI.sessionListElSelector)
      const unreadELList = listEl?.querySelectorAll(this.sI.unreadElSelector)
      if (unreadELList) {
        console.log('sendUnreadCount')
        this.sendUnReadCount(unreadELList.length)
      }
      const unreadListInfo = []
      unreadELList?.forEach((el) => {
        if (el.hasAttribute('aira-isread')) {
          return
        } else {
          el.setAttribute('aira-isread', 'true')
        }

        let nickName = unreadELList.querySelector(this.sI.sessionUserNickNameElSelector)?.innerText
        unreadListInfo.push({
          id: nickName,
          nickName
        })
      })

      if (unreadListInfo.length > 0) {
        this.sendNewFansList({
          viewSessionId: this.viewSessionId,
          platform: this.platform,
          mainAccount: this.userId,
          unreadListInfo
        })
      }
    }
  }

  // 清空whatsapp 输入框方法
  clearInput() {
    let keyDownA = {}

    if (navigator.userAgentData?.platform === 'macOS' || /Mac/.test(navigator.userAgent)) {
      keyDownA = {
        key: 'a',
        metaKey: true,
        bubbles: true,
        cancelable: true
      }
    } else {
      keyDownA = {
        key: 'a',
        ctrlKey: true,
        bubbles: true,
        cancelable: true
      }
    }
    const selectAllEvent = new KeyboardEvent('keydown', keyDownA)
    // 创建模拟删除操作的事件（这里还是以Backspace为例，也可以尝试Delete键等）
    const deleteEvent = new KeyboardEvent('keydown', {
      key: 'Backspace',
      bubbles: true,
      cancelable: true
    })
    let element = document.querySelector(this.sI.inputElSelector)
    // 先触发全选操作
    element.dispatchEvent(selectAllEvent)
    element.dispatchEvent(deleteEvent)
  }

  bindInputFunction() {
    if (location.pathname.includes('/direct/t/') || location.pathname.includes('/direct/e2ee/t/')) {
      // console.log('message page')
      const func = (timestamp) => {
        const inputEL = document.querySelector(this.sI.inputElSelector)
        // console.log(inputEL)

        if (inputEL) {
          inputEL.addEventListener(
            'keydown',
            async (event) => {
              inputEL.setAttribute('aria-bind', 'true')
              if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
                event.stopPropagation()
                event.stopImmediatePropagation()
                this.changeInputEditStatus(false)
                let config = this.translater.config
                const isTranslate = config && config.trans_over
                const v = event.target.innerText
                this.clearInput()
                if (isTranslate && v.trim()) {
                  let tV = await this.translater.translateInput(v)
                  tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, false)
                } else {
                  await this.inputMessage(v, true)
                }
                this.changeInputEditStatus(true)
                this.switchShadowState(false)
              }
            },
            {
              capture: true
            },
            true
          )

          inputEL.addEventListener('input', (event) => {
            console.log(console.log(event.target.innerText))
            event.stopPropagation()
            event.stopImmediatePropagation()
            this.createMaskDiv()
          })

          // this.isBindInputFunction = true
        } else {
          setTimeout(() => {
            window.requestAnimationFrame(func)
          }, 500)
        }
      }
      window.requestAnimationFrame(func)
    }
  }

  keyDownFunc = async (event) => {
    if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
      event.stopPropagation()
      this.changeInputEditStatus(false)
      let config = this.translater.config
      const isTranslate = config && config.trans_over
      const v = event.target.innerText
      this.clearInput()
      if (isTranslate && v.trim()) {
        let tV = await this.translater.translateInput(v)
        tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, false)
      } else {
        await this.inputMessage(v, true)
      }
      this.changeInputEditStatus(true)
      this.switchShadowState(false)
    }
  }

  sendMessageToInput({ type, message }) {
    this.inputMessage(message, type === 'send')
  }

  inputMessage(value, is_send = false) {
    console.log('value,', value)

    return new Promise((resolve) => {
      let element = document.querySelector(this.sI.inputElSelector)
      element.focus()
      const inputEvent = new InputEvent('input', {
        bubbles: true,
        cancelable: true,
        data: value,
        inputType: 'insertText'
      })
      element.dispatchEvent(inputEvent)
      element.keydown = null
      element.onkeydown = null
      if (is_send) {
        setTimeout(() => {
          document.querySelector(this.sI.sendButtonElSelector).click()
          resolve()
        }, 50)
      }
    })
  }

  switchShadowState(flag) {
    const m = document.getElementById('myShadow')
    if (!m) {
      return
    }

    if (flag) {
      m.style.display = 'block'
    } else {
      m.style.display = 'none'
    }
  }

  createMaskDiv() {
    if (!document.querySelector('#myShadow')) {
      // 创建遮罩---禁止点击按钮
      let shadwoDiv = document.createElement('div')
      shadwoDiv.id = 'myShadow' // 设置 id
      shadwoDiv.style.position = 'absolute'
      shadwoDiv.style.width = '100%'
      shadwoDiv.style.height = '100%'
      shadwoDiv.style.top = '0px'
      shadwoDiv.style.left = '0px'
      shadwoDiv.style.zIndex = 9999

      shadwoDiv.addEventListener('click', async (event) => {
        event.stopPropagation()
        this.changeInputEditStatus(false)
        let config = this.translater.config
        const isTranslate = config && config.trans_over
        const inputEl = document.querySelector(this.sI.inputElSelector)
        const v = inputEl.innerText
        this.clearInput()
        if (isTranslate && v.trim()) {
          let tV = await this.translater.translateInput(v)
          tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, false)
        } else {
          await this.inputMessage(v, true)
        }
        this.changeInputEditStatus(true)
        this.switchShadowState(false)
      })

      const maskContainer = document.querySelector(this.sI.sendButtonElSelector)
      if (maskContainer) {
        maskContainer.style.position = 'relative'
        maskContainer.appendChild(shadwoDiv)
      }
    } else {
      this.switchShadowState(true)
    }
  }

  changeInputEditStatus(editStatus) {
    let element = document.querySelector(this.sI.inputElSelector)
    if (editStatus) {
      element.removeAttribute('contenteditable')
      element.setAttribute('contenteditable', 'true')
    } else {
      element.removeAttribute('contenteditable')
      element.setAttribute('contenteditable', 'false')
    }
  }

  async getUserInfo() {
    if (window.location.pathname.includes('/direct/t/')) {
      const userEl = document.querySelector(this.sI.userElSelector)
      let isChange = false
      if (userEl) {
        if (this.userName !== userEl.innerText) {
          isChange = true
          this.userName = userEl.innerText
          this.userId = userEl.innerText
        } else {
          return
        }
        const prefilePictureEl = userEl.querySelector('image')
        const href = prefilePictureEl ? prefilePictureEl.href.baseVal : undefined
        this.prefilePicture = href ? href : undefined
      }
      if (isChange) {
        let params = {
          userId: this.userId,
          platform: this.platform,
          phone: this.userId,
          nickName: this.userName || this.userId,
          session_id: this.viewSessionId
        }
        this.sendPlatformUserInfo(params)
      }
    }
  }

  async translateList() {
    if (location.pathname.includes('/direct/t/') || location.pathname.includes('/direct/e2ee/t/')) {
      let listEL = document.querySelector(this.sI.messageListElSelector)
      if (listEL) {
        const reciveMessageElList = listEL.querySelectorAll(this.sI.reciveMessageElSelector)
        reciveMessageElList.forEach((el, index) => {
          this.translater.translateMessage(el, { type: 'in' })
        })

        const sendMessageElList = listEL.querySelectorAll(this.sI.sendMessageElSelector)
        sendMessageElList.forEach((el, index) => {
          this.translater.translateMessage(el, { type: 'out' })
        })
      }
    }
  }
}
