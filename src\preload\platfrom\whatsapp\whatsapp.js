import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { Platform } from '../platfrom.js'
import { debounce } from '@preload/utils/performance.js'
import { WhatsappTools } from './tools.js'

export class WhatsappHandler extends Platform {
  constructor(platform) {
    super(platform)
  }

  tools = new WhatsappTools()

  Store
  // #region 属性 ******************
  // WS用户信息
  userId
  userName

  // 已经新增过的粉丝信息，【，】分隔
  newFansRecord = ''
  // 当前正在聊天的用户信息
  chatUserId = ''
  isMac = navigator.userAgentData?.platform === 'macOS' || /Mac/.test(navigator.userAgent)
  // #endregion
  async init(translater) {
    await super.init(translater)
  }

  getStore() {
    try {
      if (this.Store) return
      this.Store = window.require('WAWebCollections')
      this.Store?.Msg.on('add', (msgModel) => {
        // console.log('📩 收到消息：', msgModel);
        this.getNewFans(msgModel)
        this.getChatUserId()
        setTimeout(() => {
          this.translateList()
        }, 1000)
      })
      console.group('require')
      console.log(this.Store)
      console.groupEnd()
    } catch (error) {
      console.warn(error)
    }
  }

  // #region 监听事件 ******************
  _o(mutations, observer) {
    this.getUserInfo()
    // this.updateUserInfo()
    // this.getNewFans()
    // this.getChatUserId()
    this.attBindInputKeydown()
    this.bindInputKeydown()
    this.setupDelegatedKeydownListener()
    this.autoReplyHandler()
    this.proxyListDOM()

    this.showTranslateContrast()
  }

  showTranslateContrast() {
    const el = document.querySelector('._ak1r')
    if (el) {
      const rect = el.getBoundingClientRect()
      this.translateContrast.setRect({
        top: `${rect.top - rect.height}px`,
        left: `${rect.left}px`,
        width: `${rect.width}px`,
        height: `${rect.height}px`,
        paddingLeft: `12px`,
        paddingRight: `12px`,
        boxSizing: 'border-box'
      })
      this.translateContrast.show()
      return
    }
    this.translateContrast.hide()
  }

  _isDelegatedListenerAttached = false

  _u(location) {
    this.getStore()
    this.setupDelegatedKeydownListener()
  }

  getActiveChat() {
    if (!this.Store || !this.Store.Chat) return null
    return this.Store.Chat._models.find((chat) => chat.active)
  }

  async _handleKeydownEvent(event) {
    if (this.isComposing) return
    // 检查事件的目标元素是否是我们关心的输入框
    const inputEl = event.target.closest(this.sI.input)

    if (inputEl) {
      this.setMaskBtn('mask-btn', this.sI.maskDependEl)
      this.setMaskBtn('mask-btn', this.sI.maskDependIsMerchantEl)

      if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
        event.preventDefault()
        event.stopPropagation()
        event.stopImmediatePropagation()
        this.sendMessage() // 调用您原有的发送逻辑
      } else if (event.key === 'Backspace' && inputEl.innerText.trim() === '') {
        this.translateContrast.setTextContent()
      } else {
        this._handleInputEvent(event)
      }
    }
  }
  setupDelegatedKeydownListener() {
    if (this._isDelegatedListenerAttached) {
      return // 如果已经绑定过，则直接返回
    }
    document.addEventListener('keydown', this._handleKeydownEvent.bind(this), true)

    document.addEventListener('compositionend', (event) => {
      // 输入法输入完毕时标记状态
      this.isComposing = false
    })
    document.addEventListener('compositionstart', (event) => {
      this.isComposing = true
    })
    this._isDelegatedListenerAttached = true
    console.log('Delegated keydown listener has been set up.')
  }
  _handleInputEvent = debounce(async (event) => {
    // 检查事件的目标元素是否是我们关心的输入框
    const inputEl = event.target.closest(this.sI.input)

    if (inputEl && inputEl.innerText.trim()) {
      const tv = await this.translater.translateInput(inputEl.innerText)
      this.translateContrast.setTextContent(tv)
    }
  }, 300)

  watchBodyScroll() {
    let listEL = document.querySelector(this.sI.messageListElSelector).parentElement
    if (listEL && !listEL.onscrollend) {
      listEL.onscrollend = () => {
        this.translateList()
      }
    }
  }

  observerMessageList() {
    this.mutationObserver.observerFactory(document.querySelector(this.sI.messageListElSelector), {
      subtree: true,
      childList: true,
      // 优化配置，减少不必要的观察
      attributes: false,
      characterData: false
    })
  }

  // 监听whatsapp 的dom
  proxyListDOM() {
    const d = [
      {
        target: document.querySelector(this.sI.chatListContainerSelector || '#pane-side'),
        closesetEl: this.sI.chatList
      },
      {
        target: document.querySelector(
          this.sI.searchListSelector || 'span[class="x10l6tqk x13vifvy xtijo5x x1ey2m1c x1o0tod"]'
        ),
        closesetEl: '[role="listitem"]'
      }
    ]

    d.forEach((el) => {
      if (el.target && !el.target.onclick) {
        el.target.onclick = (e) => {
          const clickUser = e.target.closest(el.closesetEl)
          if (clickUser) {
            this.getChatUserId()
            setTimeout(() => {
              this.watchBodyScroll()
              this.translateList()
            }, 1000)
          }
        }
      }
    })
  }

  autoReplyHandler() {
    this.aiReply(async () => {
      const listEl = document.querySelectorAll(this.sI.chatList)
      const unreadELList = Array.from(listEl).filter((el) => {
        return (
          el.querySelector(this.sI.unread) && el.querySelector(this.sI.unread)?.innerText !== '0'
        )
      })
      if (unreadELList.length > 0) {
        for (const element of unreadELList) {
          const rect = element.closest(this.sI.chatList)?.getBoundingClientRect()
          // 发送中心点坐标到主进程
          await this.clickAt({
            x: Math.round(rect.left + rect.width / 2),
            y: Math.round(rect.top + rect.height / 2)
          })
          let a = element.querySelector(`[aria-selected]>div`)
          a.dispatchEvent(
            new Event('focus', {
              bubbles: true,
              cancelable: true
            })
          )
          await (async () => {
            return new Promise(async (resolve, reject) => {
              setTimeout(async () => {
                const rect = element?.getBoundingClientRect()
                // 发送中心点坐标到主进程
                this.scrollToBottom()
                await this.clickAt({
                  x: Math.round(rect.left + rect.width / 2),
                  y: Math.round(rect.top + rect.height / 2)
                })
                document.querySelector(this.sI.input).click()
                document.querySelector(this.sI.input).focus()
                let messages = Array.from(document.querySelectorAll('[data-originaltext]'))
                let question = ''
                let m = messages.slice(-5).map((item) => {
                  let tlist = item.innerText.split('\n')
                  let content = tlist[0]
                  let role = ''
                  if (item.closest(this.sI.sendMessageElSelector)) {
                    role = 'user'
                    question = content
                  } else {
                    role = 'assistant'
                  }

                  return {
                    content,
                    role
                  }
                })
                let value = await this.getAiReply({
                  question,
                  messageList: m,
                  chat_id: this.userId
                })
                if (value) {
                  await this.sendMessageEvent({
                    text: value.content,
                    is_send: true,
                    input: this.sI.input,
                    // btn: this.sI.sendBtn
                    btn: `[data-icon="wds-ic-send-filled"]`
                  })
                }

                // let e = listEl[listEl.length - 1];
                this.scrollToBottom()
                let list = document.querySelector(`[role="grid"]`)
                list?.click()
                const rect2 = list?.getBoundingClientRect()
                await this.clickAt({
                  x: Math.round(rect2.left + rect2.width / 2),
                  y: Math.round(rect2.top + rect2.height / 2)
                })
                if (element.querySelector(this.sI.unread)) {
                  element.querySelector(this.sI.unread).innerText = '0'
                }
                setTimeout(() => {
                  resolve()
                }, 500)
              }, 1000)
            })
          })()
        }
      }
    })
  }

  sendMessageToInput(options) {
    this.sendMessageEvent({
      text: options.message,
      is_send: options.type === 'send',
      input: this.sI.input,
      btn: this.sI.sendBtn
    })
  }
  // #region 获取必要信息 ******************
  // 获取ws主账号信息
  async getUserInfo() {
    if (!this.userId || !this.userName) {
      let userInfo = localStorage.getItem(this.sI.userInfoKey)
      console.log('获取到ws账号信息1', userInfo)
      if (userInfo) {
        let userInfos = userInfo.replace(/\"/g, '')
        let phone_arr = userInfos.split(':')
        let phone = phone_arr[0]
        if (phone) {
          this.userId = phone

          const key = await ipcRenderer.invoke('getWhatsappNameKey', phone)
          let name = localStorage.getItem(key)
          if (name) {
            this.userName = name.match(/"(.*)"/)[1]
          }

          let param = {
            userId: phone,
            platform: this.platform,
            phone: phone,
            nickName: this.userName || phone,
            session_id: this.viewSessionId
          }
          console.log('获取到ws账号信息2', param)
          this.sendPlatformUserInfo(param)
        }
      }
    }
  }

  getIsMerchant() {
    const dividerEl = document.querySelector(this.sI.dividerElSelector)
    return dividerEl ? true : false
  }

  updateUserInfo() {
    if (this.userId) {
      let nickNameElSelector = null
      nickNameElSelector = this.getIsMerchant()
        ? this.sI.shopOwnersNickNameElSelector
        : this.sI.nickNameElSelector

      const nickNameEl = document.querySelector(nickNameElSelector)
      if (nickNameEl && !this.userName) {
        // 获取nickname
        this.userName = nickNameEl.innerText
        let param = {
          userId: this.userId,
          platform: this.platform,
          phone: this.userId,
          nickName: this.userName || this.userId,
          session_id: this.viewSessionId
        }
        console.log('获取到ws账号信息3', param)
        this.sendPlatformUserInfo(param)
      }
    }
  }

  formatChatId(chatId) {
    // 去除chatId 中的空格和加号等符号之类的只保留数字
    return chatId.replace(/\s/g, '').match(/\d+/g).join('')
  }

  // 获取当前聊天的用户信息
  getChatUserId() {
    let activeChat = this.getActiveChat()
    const activeId = activeChat?.id.user
    let activeContact = this.Store.Contact?._models.find((t) => t.id.user === activeId)

    if (activeId && this.chatUserId != activeId) {
      this.chatUserId = activeId
      console.log('当前正在聊天的用户id切换为：', this.chatUserId)
      // this.bindInputKeydown()
      this.setMaskBtn('mask-btn', this.sI.maskDependEl)
      this.setMaskBtn('mask-btn', this.sI.maskDependIsMerchantEl)
      let params = {
        mainAccount: this.userId,
        fansId: this.chatUserId,
        nickName: activeContact?.pushname ?? this.chatUserId,
        platform: this.platform
      }
      this.sendCurrentSessionFansInfo(params)
      return
    }

    // if (document.querySelector(this.sI.activeChat)) {
    //   let activeFan = ''
    //   if (document.querySelector(this.sI.personalChat)) {
    //     // 用户
    //     activeFan = document.querySelector(this.sI.activeChatId).innerText
    //   } else if (document.querySelector(this.sI.channelChat)) {
    //     // 频道
    //     activeFan = document.querySelector(this.sI.activeChatId).innerText
    //     if (activeFan) activeFan = 'lg9_' + activeFan
    //   }

    //   if (activeFan && this.chatUserId != activeFan) {
    //     this.chatUserId = this.formatChatId(activeFan)
    //     console.log('当前正在聊天的用户id切换为：', this.chatUserId)
    //     this.bindInputKeydown()
    //     this.setMaskBtn(
    //       'mask-btn',
    //       this.getIsMerchant() ? this.sI.maskDependIsMerchantEl : this.sI.maskDependEl
    //     )

    //     let params = {
    //       mainAccount: this.userId,
    //       fansId: this.chatUserId,
    //       nickName: this.chatUserId,
    //       platform: this.platform
    //     }
    //     this.sendCurrentSessionFansInfo(params)
    //   }
    // } else {
    //   this.chatUserId = ''
    // }
  }

  // 通过未读消息获取新粉丝
  getNewFans(msgModel) {
    let userId = msgModel.id.user
    if (msgModel.isNewMsg && this.userId !== userId) {
      const unreadListInfo = []
      if (this.newFansRecord.indexOf(userId) === -1) {
        this.newFansRecord += `,${userId}`

        const currentMsgModel = this.Store.Contact?._models.find((t) => t.id.user === userId)
        unreadListInfo.push({ nickName: currentMsgModel?.pushname || userId, id: userId })
      }
      if (unreadListInfo.length > 0) {
        this.sendNewFansList({
          viewSessionId: this.viewSessionId,
          platform: this.platform,
          mainAccount: this.userId,
          unreadListInfo
        })
      }
    }

    // const chats = document.querySelectorAll(this.sI.chatList)
    // if (chats) {
    //   const unreadListInfo = []
    //   let unReadCount = 0
    //   for (let chat of chats) {
    //     let no_read_user = chat.querySelector(this.sI.unread)
    //     if (no_read_user) {
    //       unReadCount += 1
    //       const phoneEl = chat.querySelector(this.sI.unreadPhone)
    //       let phone = ''
    //       if (phoneEl) phone = phoneEl.innerText
    //       let fansUserInfo = { nickName: phone, id: phone }
    //       if (this.newFansRecord.indexOf(phone) === -1) {
    //         this.newFansRecord += `,${phone}`
    //         unreadListInfo.push(fansUserInfo)
    //       }
    //     }
    //   }

    //   console.log('sendUnreadCount', unReadCount)
    //   this.sendUnReadCount(unReadCount)
    //   if (unreadListInfo.length > 0) {
    //     this.sendNewFansList({
    //       viewSessionId: this.viewSessionId,
    //       platform: this.platform,
    //       mainAccount: this.userId,
    //       unreadListInfo
    //     })
    //   }
    // }
  }
  // 绑定输入框回车事件，在发送前翻译
  // bindInputKeydown() {
  //   const func = () => {
  //     const inputEL = document.querySelector(this.sI.input)
  //     if (inputEL) {
  //       inputEL.addEventListener(
  //         'keydown',
  //         async (event) => {
  //           this.setMaskBtn(
  //             'mask-btn',
  //             this.sI.maskDependEl
  //           )
  //           this.setMaskBtn(
  //             'mask-btn',
  //             this.sI.maskDependIsMerchantEl
  //           )
  //           if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
  //             event.preventDefault()
  //             event.stopPropagation()
  //             event.stopImmediatePropagation()
  //             this.sendMessage()
  //           }
  //         },
  //         true
  //       )
  //     } else {
  //       setTimeout(() => {
  //         window.requestAnimationFrame(func)
  //       }, 100)
  //     }
  //   }
  //   window.requestAnimationFrame(func)
  // }

  bindInputKeydown() {
    const inputEL = document.querySelector(this.sI.input)
    if (!inputEL) {
      setTimeout(() => this.bindInputKeydown(), 100)
      return
    }

    if (this._keydownHandler) {
      inputEL.removeEventListener('keydown', this._keydownHandler, true)
    }

    this._keydownHandler = async (event) => {
      this.setMaskBtn('mask-btn', this.sI.maskDependEl)
      this.setMaskBtn('mask-btn', this.sI.maskDependIsMerchantEl)

      if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
        event.preventDefault()
        event.stopPropagation()
        event.stopImmediatePropagation()
        this.sendMessage()
      }
    }

    inputEL.addEventListener('keydown', this._keydownHandler, true)
  }
  // 取到当前输入框数据，翻译后发送
  async sendMessage() {
    this.setEditable(this.sI.input, false)
    try {
      // 获取输入框内容
      let contentDom = document.querySelectorAll(this.sI.inputContent)
      let v = ''
      contentDom.forEach(function (element) {
        // 打印每个元素的innerText
        if (element) {
          v += element.innerText
        }
      })

      const config = this.translater?.config
      const isTranslate = config && config.trans_over
      if (v.trim() && isTranslate) {
        this.clearInput(this.sI.input)
        let tV = await this.translater.translateInput(v)
        this.sendMessageEvent({
          text: tV || v,
          is_send: tV ? true : false,
          input: this.sI.input,
          btn: this.sI.sendBtn
        })
      } else {
        document.querySelector(this.sI.sendBtn)?.click()
        document.querySelector(this.sI.sendBtnBusiness)?.click()
      }
    } catch (error) {
      console.log(error)
      this.setEditable(this.sI.input, true)
    }
    this.setEditable(this.sI.input, true)
  }
  // #endregion

  // #region 附件发送的操作 ******************
  // 绑定附件输入框回车事件
  attBindInputKeydown() {
    const inputEL = document.querySelector(this.sI.attInput)
    if (inputEL) {
      if (inputEL.getAttribute('data-att')) {
        return
      }
      // 获取到之后给inputEL一个标记，避免重复添加事件
      inputEL.setAttribute('data-att', 'true')
      inputEL.addEventListener(
        'keydown',
        async (event) => {
          this.setMaskBtn('att-mask-btn', this.sI.attMaskDependEl)
          if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
            event.preventDefault()
            event.stopPropagation()
            event.stopImmediatePropagation()
            this.attSendMessage()
          }
        },
        true
      )
    }
  }
  // 取到当前输入框数据，翻译后发送
  async attSendMessage() {
    // 获取输入框内容
    let contentDom = document.querySelectorAll(this.sI.attInputContent)
    let v = ''
    contentDom.forEach(function (element) {
      // 打印每个元素的innerText
      if (element) {
        v += element.innerText
      }
    })
    this.setEditable(this.sI.attInput, false)
    const config = this.translater?.config
    const isTranslate = config && config.trans_over
    if (v.trim() && isTranslate) {
      this.clearInput(this.sI.attInput)
      let tV = await this.translater.translateInput(v)

      this.sendMessageEvent({
        isAtt: true,
        text: tV || v,
        is_send: tV ? true : false,
        input: this.sI.attInput,
        btn: this.sI.attSendBtn
      })
    } else {
      document.querySelector(this.sI.attSendBtn)?.click()
      this.setEditable(this.sI.attInput, true)
    }
  }
  // #endregion

  // #region 响应操作 ******************
  // 翻译列表中的历史消息
  async translateList() {
    let listEL = document.querySelector(this.sI.messageListElSelector)
    if (listEL) {
      const reciveMessageElList = listEL.querySelectorAll(this.sI.reciveMessageElSelector)
      const sendMessageElList = listEL.querySelectorAll(this.sI.sendMessageElSelector)
      const callback = () => {
        // this.scrollToBottom()
      }
      reciveMessageElList.forEach((el) => {
        this.translater.translateMessage(el, { callback: callback, type: 'in' })
      })

      sendMessageElList.forEach((el) => {
        this.translater.translateMessage(el, { callback: callback, type: 'out' })
      })
    }
  }
  // 聊天在翻译后滚动到底部
  scrollToBottom() {
    let scrollDomScroll = document.querySelector(this.sI.scrollElSelector)
    if (scrollDomScroll) {
      // 计算元素的最大滚动高度
      // var maxScrollTop = scrollDomScroll.scrollHeight - scrollDomScroll.clientHeight
      // // 将元素的scrollTop设置为其最大滚动高度，以滚动到底部
      // if (maxScrollTop - scrollDomScroll.scrollTop < 950) {
      //   scrollDomScroll.scrollTop = maxScrollTop
      // }

      scrollDomScroll.scroll({ top: scrollDomScroll.scrollHeight })
    } else {
      // 如果元素不存在，您可能想要在这里处理错误或执行其他操作
      console.log('未找到可滚动的元素', this.sI.scrollContent)
    }
  }
  // 清空whatsapp 输入框方法
  clearInput(_element) {
    let element = document.querySelector(_element)
    if (!element) return
    let keyDownA = {}
    if (this.isMac) {
      keyDownA = {
        key: 'a',
        metaKey: true,
        bubbles: true,
        cancelable: true
      }
    } else {
      keyDownA = {
        key: 'a',
        ctrlKey: true,
        bubbles: true,
        cancelable: true
      }
    }
    const selectAllEvent = new KeyboardEvent('keydown', keyDownA)
    // 创建模拟删除操作的事件（这里还是以Backspace为例，也可以尝试Delete键等）
    const deleteEvent = new KeyboardEvent('keydown', {
      key: 'Backspace',
      bubbles: true,
      cancelable: true
    })
    // 先触发全选操作
    element.dispatchEvent(selectAllEvent)
    element.dispatchEvent(deleteEvent)
  }
  // 禁用方法-- 禁止用户输入数据
  setEditable(query, editStatus) {
    try {
      let element = document.querySelector(query)
      if (!element) {
        return
      }
      if (editStatus) {
        element.removeAttribute('contenteditable')
        element.setAttribute('contenteditable', 'true')
      } else {
        element.removeAttribute('contenteditable')
        element.setAttribute('contenteditable', 'false')
      }
    } catch (error) {
      console.warn('setEditable', error)
    }
  }
  // 配置按钮蒙层，在原有发送前进行处理
  setMaskBtn(maskId, maskParent) {
    if (!document.querySelector('#' + maskId)) {
      const maskBtn = document.createElement('div')
      maskBtn.id = maskId // 设置 id
      const maskBtnParent = document.querySelector(maskParent)
      if (maskBtnParent) {
        maskBtnParent.style.position = 'relative'
        maskBtn.style.position = 'absolute'
        maskBtn.style.right = '0'
        maskBtn.style.top = '0'
        maskBtn.style.width = '50px'
        maskBtn.style.height = '100%'
        maskBtn.style.zIndex = '999'
        maskBtnParent.appendChild(maskBtn)

        maskBtn.addEventListener('click', async (e) => {
          e.stopPropagation()
          if (maskId === 'mask-btn') {
            this.sendMessage()
          } else if (maskId === 'att-mask-btn') {
            this.attSendMessage()
          }
        })
      }
    }

    // else {
    //   setTimeout(() => {
    //     this.setMaskBtn(maskId, maskParent)
    //   }, 500)
    // }
  }
  sendMessageEvent(options) {
    if (options.isAtt) {
      return new Promise((resolve) => {
        let element = document.querySelector(options.input)
        if (!element) {
          console.log('input nil')
          return
        }
        element.focus()
        // 模拟输入事件
        const inputEvent = new InputEvent('input', {
          bubbles: true,
          cancelable: true,
          data: options.text,
          inputType: 'insertText'
        })
        element.dispatchEvent(inputEvent)
        element.keydown = null
        element.onkeydown = null
        if (options.is_send) {
          setTimeout(() => {
            document.querySelector(options.btn)
              ? document.querySelector(options.btn)?.click()
              : document.querySelector(this.sI.sendBtnBusiness)?.click()
            this.setEditable(this.sI.input, true)
            this.setEditable(this.sI.attInput, true)
            resolve()
          }, 50)
        }
      })
    }
    if (this.Store) {
      this.setEditable(this.sI.input, true)

      if (!options.is_send) {
        this.tools.setTextContent(options.text)
        return Promise.resolve()
      }
      let activeChat = this.getActiveChat()
      const activeId = activeChat?.id.user
      const n = this.Store.Chat._models.find((t) => t.__x_id.user === activeId)
      window.require('WAWebSendTextMsgChatAction').sendTextMsgToChat(
        n,
        options.text,
        window.babelHelpers.extends(
          {},
          {
            quotedMsg: null,
            mentionedJidList: [],
            groupMentions: [],
            quotedMsgAdminGroupJid: null
          },
          {
            botMsgBodyType: null
          }
        )
      )
      return Promise.resolve()
    }
  }
}
