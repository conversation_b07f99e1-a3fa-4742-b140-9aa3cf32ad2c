import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { MyIdb } from './indexeddb'
import { debounce } from '../utils/performance.js'
import dayjs from 'dayjs'
import { googleEngineMap } from './googleTranslateApi.js'
export class Translater {
  config = null
  dependPlatform = null
  isNotMoney = false
  db = null

  // 内存缓存，提高性能
  memoryCache = new Map()
  maxCacheSize = 1000

  // 防抖的翻译函数
  debouncedTranslate = null

  init(dependPlatform) {
    this.db = new MyIdb()
    this.dependPlatform = dependPlatform

    // 创建防抖翻译函数
    this.debouncedTranslate = debounce(this._performTranslate.bind(this), 300)

    this.setTranslateConfig()
    ipcRenderer.on('updateViewTranslateConfig', (_event, arg) => {
      this.setTranslateConfig(arg)
      // 配置更新时清除缓存
      this.clearCache()
    })
  }

  /**
   * 设置翻译配置
   */
  async setTranslateConfig(config) {
    if (config) {
      this.config = config
      if (this.dependPlatform) {
        this.dependPlatform.translateList()
      }
      return
    }
    if (this.dependPlatform) {
      const r = await ipcRenderer.invoke('getTranslateConfig', {
        sessionId: this.dependPlatform.viewSessionId,
        isChating: true
      })
      if (r) {
        this.config = r
      }
    } else {
      throw new Error('dependPlatform is null please set....')
    }
  }

  generateCacheKey(params) {
    return `${params.text}-${params.toLang}-${params.engineCode || 'default'}`
  }

  getFromMemoryCache(cacheKey) {
    const cached = this.memoryCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < 18000000) {
      // 5小时过期
      return cached.result
    }
    return null
  }

  /**
   * 存储到内存缓存
   */
  setToMemoryCache(cacheKey, result) {
    // 如果缓存已满，删除最旧的条目
    if (this.memoryCache.size >= this.maxCacheSize) {
      const firstKey = this.memoryCache.keys().next().value
      this.memoryCache.delete(firstKey)
    }

    this.memoryCache.set(cacheKey, {
      result,
      timestamp: Date.now()
    })
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.memoryCache.clear()
    console.log('[Translater] Cache cleared')
  }

  //使用多级缓存提高性能
  async _translate(params) {
    if (!params.text || !this.config?.self) {
      return
    }

    if (/^[\d_\- .!@#$%^&*()+=]+$/.test(params.text)) {
      return params.text
    }

    const tv = await this.getResult(params)
    if (tv && tv === params.text) {
      const flag = /[\u4e00-\u9fa5]/.test(tv) && params.toLang !== 'zh'

      if (flag) {
        return ''
      }
      if (this.config.send_lang !== this.config.recive_lang) {
        return params.text
      }
    } else if (tv === '') {
      if (params.isInput && params.toLang === this.config.recive_lang) {
        return params.text
      } else {
        return '😣😣😣翻译失败...换一个引擎试试吧'
      }
    } else {
      return tv
    }
  }

  async getResult(params) {
    const cacheKey = this.generateCacheKey(params)
    let result = this.getFromMemoryCache(cacheKey)
    if (result) {
      return this.formatResponse(result)
    }

    result = await this.db.getTranslate(params)
    if (!result) {
      result = await this._performTranslate(params)
    } else {
      this.setToMemoryCache(cacheKey, result)
    }

    return this.formatResponse(result) || ''
  }

  historyTemp = []
  timmer
  /**
   * 执行实际的翻译请求
   */
  async _performTranslate(params) {
    try {
      let result
      if (googleEngineMap.has(params.engineCode)) {
        result = await ipcRenderer.invoke('googleAsyncTranslate', params)
      } else {
        result = await ipcRenderer.invoke('getMessageTranslate', JSON.stringify(params))
      }
      if (result.code === 1) {
        // 存储到内存缓存
        // 存储到 IndexedDB
        const dbData = {
          text: params.text,
          toLang: params.toLang,
          transText: result.data.result
        }
        if (params.isInput) {
          const flag = /[\u4e00-\u9fa5]/.test(result.data.result) && params.toLang !== 'zh'
          if (flag) {
            alert(`字符过多 任何数据不发送......`)
            return {}
          }
          dbData.recive_lang = this.config.recive_lang
          dbData.isInput = params.isInput
        }
        const cacheKey = this.generateCacheKey(params)
        this.setToMemoryCache(cacheKey, result)
        this.db.addTranslate(dbData)
        this.historyTemp.push({ ...params, translateText: result.data.result })
        if (this.timmer) {
          clearTimeout(this.timmer)
        }
        this.timmer = setTimeout(() => {
          if (this.historyTemp.length > 0) {
            ipcRenderer.send('getSaveChatLog', JSON.stringify(this.historyTemp))
            this.historyTemp = []
          }
        }, 1000)

        this.isNotMoney && (this.isNotMoney = false)
      } else if (result.code === 400010) {
        if (this.isNotMoney === false) {
          alert(`${result.msg}......`)
          this.isNotMoney = true
        }
      }

      return result
    } catch (error) {
      console.error('[Translater] Translation failed:', error)
      return null
    }
  }

  async translateInput(v) {
    if (!(this.config && this.config.trans_over)) {
      return v
    }
    let params = {
      text: v.split('\n').join(''),
      engineCode: this.config.engineCode,
      fromLang: 'auto',
      toLang: this.config.send_lang,
      recive_lang: this.config.recive_lang,
      time: dayjs(Date.now()).format('YYYY-MM-DD HH:mm:ss'), // 接收时间
      type: 'out', // 接收a
      ccountId: this.dependPlatform.userId,
      fansId: this.dependPlatform.chatUserId,
      platform: this.dependPlatform.platform
    }

    const result = await this._translate({ isInput: true, ...params })
    if (result === '😣😣😣翻译失败...换一个引擎试试吧') {
      alert('翻译失败... 无法发送😶😶😶')
      return ''
    }

    return result
  }

  createLoadingDiv(v) {
    var loadingDiv = document.createElement('div')
    loadingDiv.style.display = 'flex'
    loadingDiv.style.flexDirection = 'column'

    loadingDiv.insertAdjacentHTML('afterbegin', `<span>${v}</span>`)
    loadingDiv.insertAdjacentHTML(
      'beforeend',
      `<div style="width:100%; margin: 5px 0px; height: 0; border-bottom: 1px dashed #000"></div>
      <div class="__translateloading">
      <div class="dot"></div>
      <div class="dot"></div>
      <div class="dot"></div>
      </div>`
    )
    return loadingDiv
  }

  async translateMessage(element, options = {}) {
    if (!this.config.self) {
      return
    }

    if (element.hasAttribute('data-translated')) {
      if (
        element.getAttribute('data-translated') === this.config.recive_lang ||
        element.getAttribute('data-translated') === 'tranlating'
      ) {
        return true
      }
    } else {
      element.setAttribute('data-originaltext', element.innerText)
    }

    element.setAttribute('data-translated', 'tranlating')
    element.replaceChildren(this.createLoadingDiv(element.innerText))
    let v = element.getAttribute('data-originaltext').trim()
    let params = {
      text: v.split('\n').join(''),
      engineCode: this.config.engineCode,
      fromLang: 'auto',
      toLang: this.config.recive_lang,
      time: dayjs(Date.now()).format('YYYY-MM-DD HH:mm:ss'), // 接收时间
      type: options.type || 'out', // 接收a
      ccountId: this.dependPlatform.userId,
      fansId: options.fansId || this.dependPlatform.chatUserId,
      platform: this.dependPlatform.platform
    }
    const tv = await this._translate(params)
    // 待插入的html
    var html = document.createElement('div')
    html.insertAdjacentHTML('beforeend', `<span>${v}</span>`)
    if (tv) {
      html.insertAdjacentHTML(
        'beforeend',
        `<div style="width:100%; margin: 5px 0px; height: 0; border-bottom: 1px dashed currentColor"></div>`
      )
      html.insertAdjacentHTML('beforeend', `<span>${tv}</span>`)
    }

    element.replaceChildren(html)
    element.setAttribute('data-translated', this.config.recive_lang ? this.config.recive_lang : '')
    if (options.callback) options.callback()
  }

  formatResponse(res) {
    let tv = ''
    if (res.hasOwnProperty('code') && res.code === 1) {
      tv = res.data.result
    } else if (res.value) {
      tv = res.value
    }
    return tv
  }
}

class TranslateMessageInfo {
  constructor({ engineCode, fromLang, toLang, text, translatedText }) {
    this.engineCode = engineCode
    this.fromLang = fromLang
    this.toLang = toLang
    this.text = text
    this.translatedText = translatedText
  }
}
