const { parentPort } = require("worker_threads")
const { googleAsyncTranslate } = require("../utils/googleTranslateApi.js")

parentPort.on("message", (params) => {
  console.log("translate", params)
  try {
    googleAsyncTranslate(params).then((res) => {
      parentPort.postMessage({
        id: params.id,
        result: res,
      })
    })
  } catch (error) {
    parentPort.emit("messageerror", error)
  }
})
