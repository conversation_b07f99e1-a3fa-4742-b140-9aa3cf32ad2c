import { ipcMain } from 'electron'
import { GlobalObject } from '@main/global/'
import { googleAsyncTranslate } from "@main/utils/googleTranslateApi.js"
import { createWorker } from "@main/service/thread.js"

export default class TransalteApi {
  pendingMap = new Map()

  getTranslateConfig() {
    ipcMain.handle('getTranslateConfig', async (event, { sessionId, isChating }) => {
      let config
      if (sessionId && isChating) {
        config = await GlobalObject.translateDB.getTranslate({
          sessionId: sessionId
        })
        if (!config) {
          config = GlobalObject.translateConfig
          GlobalObject.translateDB.setTranslate({
            sessionId,
            config
          })
        }
      } else {
        config = GlobalObject.translateConfig
      }

      return config || {}
    })
  }
  updateTranslateConfig() {
    // 接收翻译配置
    ipcMain.on('updateTranslateConfig', (event, { config, isChating, sessionId }) => {
      console.log('翻译配置更新', { config, isChating })
      if (isChating && sessionId) {
        GlobalObject.translateDB.setTranslate({
          sessionId,
          config
        })
        GlobalObject.viewManager.viewsMap
          .get(sessionId)
          .webContents.send('updateViewTranslateConfig', config)
      } else {
        GlobalObject.translateConfig = config
      }
    })
  }
  setTranslateConfig() {
    // 接收翻译配置
    ipcMain.on('setTranslateConfig', (event, { config, sessionId }) => {
      console.log('create config', config)

      if (sessionId) {
        GlobalObject.translateDB.setTranslate({
          sessionId,
          config
        })
      }
    })
  }

  googleAsyncTranslate() {
    // let worker = createWorker()
    // let idCounter = 0
    // worker.on("message", (res) => {
    //   console.log("worker", res)
    //   const { id, result } = res
    //   const { resolve } = this.pendingMap.get(id)
    //   resolve(result)
    //   this.pendingMap.delete(id)
    // })

    // worker.on('error', (err) => {
    //   console.error('[Worker 错误]', err);
    //   for (const { reject } of this.pendingMap.values()) reject(err);
    //   this.pendingMap.clear();
    // });

    // worker.on("exit", (code) => {
    //   console.log("worker exit");
    //   if (code !== 0) {
    //     worker = createWorker()
    //   }
    // })

    ipcMain.handle('googleAsyncTranslate', (_event, params) => {
      console.log('googleAsyncTranslate')

      if (params.text.length) return googleAsyncTranslate(params)
      return new Promise((resolve, reject) => {
        this.pendingMap.set(idCounter, { resolve, reject })
        worker.postMessage({ ...params, id: idCounter })
        idCounter += 1
      })
    })
  }
}
