import { contextBridge } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { getPlatform, Translater } from './platfrom/index.js'

// 动态分割
const PLATFORM_HANDLERS = {
  facebook: () => import('./platfrom/facebook.js').then((m) => m.FacebookHandler),
  whatsapp: () => import('./platfrom/whatsapp/index.js').then((m) => m.WhatsappHandler),
  telegram: () => import('./platfrom/telegram.js').then((m) => m.TelegramHandler),
  instagram: () => import('./platfrom/instagram.js').then((m) => m.InstagramHandler),
  tiktok: () => import('./platfrom/tiktok.js').then((m) => m.TiktokHandler),
  facebookBusiness: () =>
    import('./platfrom/facebookBusiness.js').then((m) => m.FacebookBusinessHandler),
  twitter: () => import('./platfrom/twitter.js').then((m) => m.TwitterHandler),
  discord: () => import('./platfrom/discord.js').then((m) => m.DiscordHandler)
}

// 性能监控传入mian目录
const performanceMonitor = {
  startTime: performance.now(),

  mark(label) {
    const now = performance.now()
    console.log(`[Preload Performance] ${label}: ${(now - this.startTime).toFixed(2)}ms`)
    this.startTime = now
  }
}

try {
  let platformHandler = null

  const initElectronAPI = () => {
    performanceMonitor.mark('Start Electron API init')

    // Use `contextBridge` APIs to expose Electron APIs to
    // renderer only if context isolation is enabled, otherwise
    // just add to the DOM global.
    const platform = {
      isWin: process.platform === 'win32',
      isMac: process.platform === 'darwin',
      isLinux: process.platform === 'linux'
    }
    if (process.contextIsolated) {
      contextBridge.exposeInMainWorld('electron', {
        ...electronAPI,
        ipcRenderer: {
          ...electronAPI.ipcRenderer,
          // on: (...args) => {
          //   electronAPI.ipcRenderer.on(...args)
          // },
          send: (...args) => {
            electronAPI.ipcRenderer.send(...args)
          },
          invoke: (...args) => {
            return electronAPI.ipcRenderer.invoke(...args)
          }
        }
      })
      contextBridge.exposeInMainWorld('platform', platform)
    } else {
      window.electron = electronAPI
      window.platform = platform
    }

    performanceMonitor.mark('Electron API initialized')
  }

  const initPlatformHandler = async () => {
    performanceMonitor.mark('Start platform detection')

    const platform = getPlatform()
    if (!platform) {
      console.warn('[Preload] No platform detected')
      return
    }

    performanceMonitor.mark(`Platform detected: ${platform}`)

    const handlerLoader = PLATFORM_HANDLERS[platform]
    if (!handlerLoader) {
      console.error(`[Preload] No handler found for platform: ${platform}`)
      return
    }

    try {
      // 动态加载对应平台的处理器
      const HandlerClass = await handlerLoader()
      performanceMonitor.mark(`Handler loaded for ${platform}`)

      // 创建处理器实例
      platformHandler = new HandlerClass(platform)

      // 初始化翻译器
      const translater = new Translater()

      // 初始化平台处理器
      await platformHandler.init(translater)
      if (import.meta.env.DEV) {
        if (process.contextIsolated) {
          contextBridge.exposeInMainWorld('platformHandler', platformHandler)
        } else {
          window.platformHandler = platformHandler
        }
      }
     
      performanceMonitor.mark(`Platform handler initialized for ${platform}`)
    } catch (error) {
      console.error(`[Preload] Failed to load handler for ${platform}:`, error)
    }
  }

  const init = async () => {
    performanceMonitor.mark('Preload initialization started')

    initElectronAPI()
    await initPlatformHandler()

    performanceMonitor.mark('Preload initialization completed')
  }
  init()

  // if (typeof requestIdleCallback !== 'undefined') {
  //   requestIdleCallback(() => init(), { timeout: 1000 })
  // } else {
  //   // 降级到 setTimeout
  //   setTimeout(init, 0)
  // }
} catch (error) {
  console.error('[Preload] Initialization error:', error)
}
