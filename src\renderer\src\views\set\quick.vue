<template>
	<div class="sys_base">
		<!-- 系统导航 -->
		<!-- <div :class="showHideWord?'sys_left':'sys_left sys_left_b'">
			<Tabar :softName='chatName' @changeChat='changeChat' @showHideWordFun='showHideWordFun'></Tabar>
		</div> -->
		<!-- 主要内容区 -->
		<div :class="tabarStore.showHideWord ? 'main_cont quick_main_cont' : 'main_cont quick_main_cont small_tab'">
			<!-- 联系我们 -->
			<div class="quick_cont">
				<div class="cont_left">
					<div class="tit">
						<h2>个人快捷回复</h2>
						<span>Personal quick reply</span>
					</div>
					<div class="search">
						<div class="menu_btn">
							<p>目录</p>
							<div @click="addQuickCateFun">
								<img :src="icon50" alt="">
								<span>添加分组</span>
							</div>
						</div>
						<div class="input">
							<input type="text" @input="searchInput" v-model="searchName" placeholder="输入关键字进行过滤">
						</div>
					</div>
					<div class="teamList">
						<div class="items" v-for="(item, index) in quick_data">
							<div :class="item.is_show ? 'name name_click' : 'name'">
								<div class="name_son" v-if="item.type == 'over'">
									<span @click="closeOpenSonContFun(index)">{{ item.name }}</span>
									<div class="addSon" @click="addQuickCateSonFun(index)">
										<img :src="icon50" alt="">
									</div>
									<div class="addSon" @click="delQuickCateSonFun(index, '-1')">
										<img :src="del" alt="">
									</div>
								</div>
								<el-input v-if="item.type == 'edit'" @blur="getEditInfo(index, -1)" v-model="quick_data[index].name"
									placeholder="请输入快"></el-input>
							</div>
							<div class="item" v-if="item.is_show" v-for="(ite, ind) in item.quickReply">
								<div :class="(selCateIndex.index == index && selCateIndex.ind == ind) ? 'name sel_names' : 'name'">
									<div class="name_son" v-if="ite.type == 'over'" @click="selCateAddTextFun(index, ind)">
										<span>{{ ite.name }}</span>
										<div v-if="ite.type == 'over'" class="addSon" @click="editQuickCateSonFun(index, ind)">
											<img :src="editIcon" alt="">
										</div>
										<div class="addSon" @click="delQuickCateSonFun(index, ind)">
											<img :src="del" alt="">
										</div>
									</div>
									<el-input v-if="ite.type == 'edit'" @blur="getEditInfo(index, ind)"
										v-model="quick_data[index].quickReply[ind].name" placeholder="请输入"></el-input>

								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="cont_right">
					<div class="right_header">
						<div class="item" @click="addQuickFun">
							<img :src="icon59" alt="">
							<p>添加文字</p>
						</div>
						<!-- <div class="item">
							<span>+</span>
							<p>添加名片</p>
						</div> -->
					</div>
					<div class="quick_message">
						<el-form ref="form" :model="qicon_cont" @submit.native.prevent>
							<div class="item" v-for="(item, index) in qicon_cont">
								<div class="input">
									<el-input @blur="getEditInfoText" v-model="qicon_cont[index].message"
										placeholder="请输入快捷回复内容"></el-input>
								</div>
								<div class="del" @click="delQuickCont(index)">
									<img :src="del" alt="">
								</div>
							</div>
						</el-form>

					</div>
				</div>
			</div>
		</div>
	</div>

</template>

<script setup>

import {
	ref, watch,
	onMounted,
	onUnmounted
} from 'vue'
import { ElMessage } from 'element-plus'
// import {
// 	getWorkOrderSql
// } from "../../api/account.js"
import { useRouter, useRoute } from 'vue-router'  // 导入 useRouter
import { useTabStore } from "@renderer/store/index.js"
const tabarStore = useTabStore()
const router = useRouter()  // 获取路由实例
const route = useRoute();  // 获取当前路由对象
// 导入本地图片
const icon50 = new URL('../../assets/img/icon50.svg', import.meta.url).href;
const icon55 = new URL('../../assets/img/icon55.svg', import.meta.url).href;
const icon56 = new URL('../../assets/img/icon56.svg', import.meta.url).href;
const icon57 = new URL('../../assets/img/icon57.svg', import.meta.url).href;
const icon59 = new URL('../../assets/img/icon59.svg', import.meta.url).href;
const del = new URL('../../assets/img/del.png', import.meta.url).href;
const editIcon = new URL('../../assets/img/edit.png', import.meta.url).href;


// 个人快捷回复 设置内容==================
const quick_data = ref([])
// 添加快捷回复分组
const addQuickCateFun = () => {
	if (quick_data.value.length > 0) {
		for (let i = 0; i < quick_data.value.length; i++) {
			quick_data.value[i].type = 'over'
		}
	}
	quick_data.value.unshift({ 'name': '新建分组', 'is_show': false, 'type': 'edit', 'quickReply': [] })
	inputCont.value = '新建分组'
	localStorage.setItem('selfQuick', JSON.stringify(quick_data.value))
}
// 搜索框输入
const searchName = ref('')
const searchInput = () => {
	let quick_list = JSON.parse(localStorage.getItem('selfQuick'))
	let over_q_arr = []
	for (let i = 0; i < quick_list.length; i++) {
		let quick_arr = []
		for (let j = 0; j < quick_list[i].quickReply.length; j++) {
			if (quick_list[i].quickReply[j].name.indexOf(searchName.value) !== -1) {
				quick_arr.push(quick_list[i].quickReply[j])
			}
		}
		if (quick_arr.length > 0) {
			quick_list[i].quickReply = quick_arr
			over_q_arr.push(quick_list[i])
		}
	}
	if (searchName.value == '' || !searchName.value) {
		quick_data.value = JSON.parse(localStorage.getItem('selfQuick'))
	} else {
		quick_data.value = over_q_arr
	}

}
// 获取当前选中的input框--监听输入
const inputCont = ref()
const getEditInfo = (index, sonIndex) => {
	if (sonIndex < 0) {
		// quick_data.value[index].name=inputCont.value
		if (quick_data.value[index].name && quick_data.value[index].name != '') {
			quick_data.value[index].type = 'over'
		}

	} else {
		// quick_data.value[index].quickReply[sonIndex].name=inputCont.value
		if (quick_data.value[index].quickReply[sonIndex].name && quick_data.value[index].quickReply[sonIndex].name != '') {
			quick_data.value[index].quickReply[sonIndex].type = 'over'
		}
	}
	localStorage.setItem('selfQuick', JSON.stringify(quick_data.value))
}
// 添加子级回复栏目
const addQuickCateSonFun = (index) => {
	let is_add = true // 是否可以增加
	for (let i = 0; i < quick_data.value.length; i++) {

		if (!quick_data.value[i].name) {
			is_add = false
		} else {
			quick_data.value[i].type = 'over'
		}
		for (let j = 0; j < quick_data.value[i].quickReply.length; j++) {
			if (!quick_data.value[i].quickReply[j].name) {
				is_add = false
			} else {
				quick_data.value[i].quickReply[j].type = 'over'
			}
		}
	}
	if (is_add) {
		quick_data.value[index].is_show = true
		quick_data.value[index].quickReply.unshift({ 'name': '新建分类', 'is_sel': false, 'type': 'edit', 'text': [] })
		localStorage.setItem('selfQuick', JSON.stringify(quick_data.value))
	} else {
		ElMessage({
			showClose: true,
			message: '有为空的类目，请先完善再增加',
			type: 'info'
		})
	}

}
// 删除当前分组
const delQuickCateSonFun = (index, ind) => {
	// qicon_cont.value.splice(index,1)
	if (ind == -1) {
		quick_data.value.splice(index, 1)
	} else {
		quick_data.value[index].quickReply.splice(ind, 1)
	}
	localStorage.setItem('selfQuick', JSON.stringify(quick_data.value))
}
// 关闭 打开 子级栏目
const closeOpenSonContFun = (index) => {
	quick_data.value[index].is_show = !quick_data.value[index].is_show
}
// 选中子级栏目添加内容
const selCateIndex = ref({ 'index': -1, 'ind': -1 })
const selCateAddTextFun = (index, ind) => {
	selCateIndex.value = {
		'index': index,
		'ind': ind
	}
	// 选择当前选择栏目的内容
	qicon_cont.value = quick_data.value[index].quickReply[ind].text
}
// 设置子栏目为可编辑
const editQuickCateSonFun = (index, ind) => {
	quick_data.value[index].quickReply[ind].type = 'edit'
	inputCont.value = quick_data.value[index].quickReply[ind].name
	localStorage.setItem('selfQuick', JSON.stringify(quick_data.value))
}
onMounted(() => {
	if (localStorage.getItem('selfQuick')) {
		quick_data.value = JSON.parse(localStorage.getItem('selfQuick'))
	}

})
// 改变平台=====================================================
const changeChat = (cN) => {
	chatName.value = cN
}
// 设置是否显示设置信息页面
const is_open = ref(false)
const openSetPage = (son_b) => {
	is_open.value = son_b
}
// 快捷回复内容
const qicon_cont = ref([])
// 添加快捷回复内容
const addQuickFun = () => {
	if (selCateIndex.value.index == -1 || selCateIndex.value.ind == -1) {
		ElMessage({
			showClose: true,
			message: '请先选择分类',
			type: 'info'
		})
	} else {
		// qicon_cont.value.push({'message':''})
		quick_data.value[selCateIndex.value.index].quickReply[selCateIndex.value.ind].text.unshift({ 'message': '' })
		localStorage.setItem('selfQuick', JSON.stringify(quick_data.value))
	}

}
const getEditInfoText = () => {
	let index = selCateIndex.value.index
	let ind = selCateIndex.value.ind
	quick_data.value[index].quickReply[ind].text = qicon_cont.value
	localStorage.setItem('selfQuick', JSON.stringify(quick_data.value))
}
// 删除快捷回复内容
const delQuickCont = (index) => {
	qicon_cont.value.splice(index, 1)
	let indexP = selCateIndex.value.index
	let ind = selCateIndex.value.ind
	quick_data.value[indexP].quickReply[ind].text = qicon_cont.value
	localStorage.setItem('selfQuick', JSON.stringify(quick_data.value))
}
// 传递左侧栏目隐藏或展示文字方法
const showHideWord = ref(true)
watch(
	() => route.query,
	(newParams, oldParams) => {
		// 设置左侧是否显示
		if (newParams.chat == 'slide') {
			showHideWord.value = newParams.type == 'true' ? true : false
		}
	},
	{ immediate: true } // 立即执行一次，以确保初始参数也被捕获
);
</script>

<style lang="scss">
@import url("../../assets/style/quick.scss");
</style>