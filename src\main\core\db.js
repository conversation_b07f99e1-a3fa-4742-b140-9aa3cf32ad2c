import { Low } from 'lowdb'
import { JSONFile } from 'lowdb/node'
import fs from 'fs/promises'
import path from 'path'

export class LowDB {
  constructor(
    file = 'db.json',
    { defaultData } = {
      defaultData: {}
    }
  ) {
    const adapter = new JSONFile(file)
    this.db = new Low(adapter, defaultData)
  }

  async init() {
    try {
      await this.db.read()
      this.db.data ||= this.db.defaultData
      await this.db.write()
    } catch (error) {
      this.db.data = {}
      await this.db.write()
    }
  }
  async save() {
    await this.db.write()
  }

  async getAll(collection) {
    await this.db.read()
    return this.db.data[collection] || []
  }

  async getOne(collection, filter) {
    await this.db.read()
    const items = this.db.data[collection] || []
    const item = items.find((i) => this.#matches(i, filter))
    return item
  }

  async create(collection, item) {
    await this.db.read()
    this.db.data[collection] ||= []
    this.db.data[collection].push(item)
    await this.db.write()
    return item
  }

  async update(collection, filter, updates) {
    await this.db.read()
    const items = this.db.data[collection] || []
    const item = items.find((i) => this.#matches(i, filter))
    if (item) {
      console.log('item', item)
      Object.assign(item, updates)
      console.log('upitem', item)
    }

    await this.db.write()
    return item
  }

  async remove(collection, filter) {
    await this.db.read()
    const items = this.db.data[collection] || []
    this.db.data[collection] = items.filter((item) => !this.#matches(item, filter))
    await this.db.write()
  }

  // 私有方法：对象匹配
  #matches(item, filter) {
    return Object.entries(filter).every(([key, value]) => item[key] === value)
  }
}

// 判断路径是文件还是目录，并递归创建或处理
export async function createOrOpenPath(filePath) {
  try {
    const stats = await fs.stat(filePath)

    // 路径已存在
    if (stats.isDirectory()) {
      console.log(`目录已存在: ${filePath}`)
      return 'directory_exists'
    } else {
      console.log(`文件已存在: ${filePath}`)
      return 'file_exists'
    }
  } catch (error) {
    // 路径不存在，需要创建
    if (error.code === 'ENOENT') {
      console.log(`路径不存在，正在创建: ${filePath}`)

      // 检查路径最后一项是否有文件扩展名
      const ext = path.extname(filePath)

      if (ext) {
        // 这是文件路径，需要先创建父目录
        const dir = path.dirname(filePath)
        await fs.mkdir(dir, { recursive: true })
        // 创建空文件
        await fs.writeFile(filePath, '')
        console.log(`文件及其父目录已成功创建: ${filePath}`)
        return 'file_created'
      } else {
        // 这是目录路径
        await fs.mkdir(filePath, { recursive: true })
        console.log(`目录及其父目录已成功创建: ${filePath}`)
        return 'directory_created'
      }
    } else {
      // 处理其他可能的错误
      console.error(`处理路径时发生错误: ${error.message}`)
      return 'error'
    }
  }
}
