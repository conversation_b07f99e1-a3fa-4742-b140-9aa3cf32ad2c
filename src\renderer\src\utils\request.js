import axios from 'axios'
import CryptoJS from 'crypto-js'
import { ElMessage } from 'element-plus'
import router from '../router'
import useLoadingStore from "@renderer/store/loading"
import { pinia } from "@renderer/store/pinia"
const loadingStore = useLoadingStore(pinia)
// 导入router
// import { useRouter } from 'vue-router'  // 导入 useRouter
// const router = useRouter()  // 获取路由实例
// create an axios instance
let isShowError = false
const service = axios.create({
  baseURL:
    import.meta.env.MODE === 'development'
      ? import.meta.env.VITE_BASE_API_PROXY
      : import.meta.env.VITE_BASE_API, // url = base url + request url
  // baseURL:"https://cherun.sdmuchen.cn/index.php/api/", // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 10000, // request timeout
  withCredentials: true,
  headers: {
    // 跨域请求 这个配置不能少
    // "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    'Content-Type': 'application/json;charset=UTF-8',
    // 'Accept': 'application/json',
    'Access-Control-Allow-Origin': '*'
    // "satoken":localStorage.getItem('satoken'),
  },
  isLoading: false

  // params:{'token':'mzdesoieurthdsf1223ldf'}
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    if (config.isLoading) {
      loadingStore.loadingStatus = true
    }

    // do something before request is sent
    // if (store.getters.token) {
    // let each request carry token
    // ['X-Token'] is a custom headers key
    // please modify it according to the actual situation
    // config.headers['X-Token'] = getToken()
    // config.headers['Authorization'] = '434f4a85681342baf8d489f110dd8e49';
    // }
    const currentTime = new Date() // 创建一个Date对象，表示当前时间
    const timestamp = Math.floor(currentTime.getTime() / 1000)
    // const timestamp = Date.now()
    const token = localStorage.getItem('token') ? localStorage.getItem('token') : ''
    config.headers.token = token
    config.headers.timestamp = timestamp
    config.headers.sign = sha256(JSON.stringify(config.data) + timestamp + token)
    return config
  },
  (error) => {
    // do something with request error
    return Promise.reject(error)
  }
)
window.isExpire = false
// response interceptor
service.interceptors.response.use(
  async (response) => {
    loadingStore.loadingStatus = false
    const res = response.data
    // if the custom code is not 20000, it is judged as an error.
    // 40006 token错误

    if (res.code == 40006) {
      if (!window.isExpire) {
        window.isExpire = true

        await window.electron.ipcRenderer.invoke('onUserLoginOut', '登录凭证过期，请重新登录')

        localStorage.removeItem('token')
        router.replace('/login')
      }
      return res
    } else if (res.code !== 1) {
      // context.$message.error(res.msg || '出错了！')
      return res
    } else {
      return res
    }
  },
  async (error) => {
    if (error.config.isLoading) {
      loadingStore.loadingStatus = false
    }
    if (error.code === 'ECONNABORTED' && error.config.url === '/api/translate/translate') {
      let config = error.config
      try {
        let res = await axios.get(
          `https://translate.google.com/translate_a/single?client=at&sl=${config.fromLang}&tl=${config.toLang}&dt=t&q=${config.text}`
        )
        let data = res.data
        let tranlateText = ''
        data[0].forEach((item) => {
          tranlateText += item[0]
        })
        return {
          code: 1,
          data: {
            result: tranlateText
          }
        }
      } catch (error) {
        return error
      }
    }
    if (!isShowError) {
      isShowError = true
      ElMessage({
        showClose: true,
        message: '网络超时，请检查您的网络连接或稍后重试',
        type: 'warning',
        onClose: () => {
          isShowError = false
        }
      })
    }
    const currentTime = new Date() // 创建一个Date对象，表示当前时间
    let timestamp = Math.floor(currentTime.getTime() / 1000)
    // return {'code':999999,'mes':'网络报错'}
    return Promise.reject(error)
  }
)
function sha256(message) {
  return CryptoJS.SHA256(message).toString()
}

export default service
