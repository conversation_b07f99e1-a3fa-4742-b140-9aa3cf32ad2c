import { app, BrowserWindow, ipcMain, session, systemPreferences } from 'electron'
import { createWindow } from '@main/service/window.js'
import { ViewManager } from '@main/service/viewManager.js'
import { MessageManager } from '@main/service/messageManager.js'
import path, { join } from 'path'
import { electronApp, optimizer, platform } from '@electron-toolkit/utils'
import { getTranslate, saveChat, getSelectorInfoFunc } from '../renderer/src/utils/translate.js'
import { GlobalObject } from './global/index'
import { mountIpcApi } from './control/index.js'
import { initShortcut } from './service/shortcut.js'
import { getDatasetRolePrompt } from './service/repley'
import { TranslateDB } from './service/translate.js'
// 优化的 preload 路径映射

console.log('import.meta.env', import.meta.env)
export const preloadJsPathMap = new Map([
  ['facebook', join(__dirname, '../preload/index.js')],
  ['instagram', join(__dirname, '../preload/index.js')],
  ['telegram', join(__dirname, '../preload/index.js')],
  ['whatsapp', join(__dirname, '../preload/index.js')],
  ['zalo', join(__dirname, '../preload/index.js')],
  ['tiktok', join(__dirname, '../preload/index.js')],
  ['facebookBusiness', join(__dirname, '../preload/index.js')],
  ['twitter', join(__dirname, '../preload/index.js')],
  ['discord', join(__dirname, '../preload/index.js')]
])
process.on('uncaughtException', (error) => {
  console.error('[MainProcess UncaughtException]', error)
})

process.on('unhandledRejection', (reason) => {
  console.error('[MainProcess UnhandledRejection]', reason)
})

// 性能监控配置
const PERFORMANCE_CONFIG = {
  enableLogging: !app.isPackaged, // 开发环境启用性能日志
  enableMemoryMonitoring: true,
  maxMemoryUsage: 512 * 1024 * 1024 // 512MB
}

export const queryParams = {
  uuid: ''
}
try {
  const getSelectorInfo = async (accessToken) => {
    const res = await getSelectorInfoFunc({}, accessToken)
    const r = {
      facebook: {
        userElSelector: 'div.x1iyjqo2>div>ul>li:nth-child(1) a',
        sessionUserA: `div[data-pagelet="MWThreadList"]>div.x1qjc9v5.x9f619.xdl72j9.x2lwn1j.xeuugli.x1n2onr6.x1ja2u2z.x78zum5.xdt5ytf.x1iyjqo2.xs83m0k.x6ikm8r.x10wlt62 a[role='link']`,
        sessionUserNickNameElSelector:
          "a[role='link'] [class='x1lliihq x6ikm8r x10wlt62 x1n2onr6 xlyipyv xuxw1ft']",
        unreadElSelector: 'span.x1spa7qu',
        sessionListElSelector:
          'div[data-pagelet="MWThreadList"]>div.x1qjc9v5.x9f619.xdl72j9.x2lwn1j.xeuugli.x1n2onr6.x1ja2u2z.x78zum5.xdt5ytf.x1iyjqo2.xs83m0k.x6ikm8r.x10wlt62',
        sendButtonElSelector:
          'div.xuk3077.x57kliw.x78zum5.x6prxxf.xz9dl7a.xsag5q8>span.html-span:last-child',
        sendButtonElSelector:
          'div.xuk3077.x57kliw.x78zum5.x6prxxf.xz9dl7a.xsag5q8>span.html-span:last-child div[role="button"]',
        inputElSelector: 'div.notranslate',
        messageListElSelector:
          "[class='x78zum5 xdt5ytf x1iyjqo2 x6ikm8r x1odjw0f xish69e x16o0dkt']",
        reciveMessageElSelector:
          '[class="html-div xexx8yu xyri2b x18d9i69 x1c1uobl x1gslohp x14z9mp x12nagc x1lziwak x1yc453h x126k92a x18lvrbx"]',
        sendMessageElSelector:
          '[class="html-div xexx8yu xyri2b x18d9i69 x1c1uobl x1gslohp x14z9mp x12nagc x1lziwak x1yc453h x126k92a xyk4ms5"]',
        // 小窗
        activeSmall: '.x1eqhsl0',
        smallInputElSelector: 'div.xzsf02u.x1a2a7pz.x1n2onr6.x14wi4xw',
        smallSendButtonElSelector: '[aria-label="按 Enter 键发送"]',
        smallBtnParent: '',
        activeSmallId:
          'a[class="x1i10hfl x1qjc9v5 xjbqb8w xjqpnuy xc5r6h4 xqeqjp1 x1phubyo x13fuv20 x18b5jzi x1q0q8m5 x1t7ytsu x972fbf x10w94by x1qhh985 x14e42zd x9f619 x1ypdohk xdl72j9 x2lah0s xe8uvvx xdj266r x14z9mp xat24cr x1lziwak x2lwn1j xeuugli xexx8yu xyri2b x18d9i69 x1c1uobl x1n2onr6 x16tdsg8 x1hl2dhg xggy1nq x1ja2u2z x1t137rt x1fmog5m xu25z0z x140muxe xo1y3bh x1q0g3np x87ps6o x1lku1pv x1a2a7pz x78zum5"]',
        activeSmallNickName: '.xxymvpz.x1dyh7pn'
      },
      instagram: {
        userElSelector:
          'div[class="x1i10hfl xjbqb8w x1ejq31n xd10rxx x1sy0etr x17r0tee x972fbf xcfux6l x1qhh985 xm0m39n x9f619 x1ypdohk xe8uvvx xdj266r x11i5rnm xat24cr x1mh8g0r xexx8yu x4uap5 x18d9i69 xkhd6sd x16tdsg8 x1hl2dhg xggy1nq x1a2a7pz x6s0dn4 x78zum5 xs83m0k xeuugli x1d5wrs8"]',
        sessionUserNickNameElSelector: `div[class="x9f619 xjbqb8w x78zum5 x168nmei x13lgxp2 x5pf9jr xo71vjh x1uhb9sk x1plvlek xryxfnj x1c4vz4f x2lah0s x1q0g3np xqjyukv x6s0dn4 x1oa3qoh x1nhvcw1"]`,
        sessionCurrentUserElSelector: `div[class="x1i10hfl x1qjc9v5 xjqpnuy xa49m3k xqeqjp1 x2hbi6w x13fuv20 xu3j5b3 x1q0q8m5 x26u7qi x972fbf xcfux6l x1qhh985 xm0m39n x9f619 x1ypdohk xdl72j9 x2lah0s xe8uvvx x2lwn1j xeuugli x1n2onr6 x16tdsg8 x1hl2dhg xggy1nq x1ja2u2z x1t137rt x1q0g3np x87ps6o x1lku1pv x1a2a7pz x168nmei x13lgxp2 x5pf9jr xo71vjh x1lliihq xdj266r x11i5rnm xat24cr x1mh8g0r x1y1aw1k xwib8y2 xbbxn1n xxbr6pl x19g9edo xmoo3j9 x1e9wozp xoo1adm"]`,
        unreadElSelector:
          '.x9f619.x1ja2u2z.xzpqnlu.x1hyvwdk.x14bfe9o.xjm9jq1.x6ikm8r.x10wlt62.x10l6tqk.x1i1rx1s',
        sessionListElSelector: 'div.x78zum5.xdt5ytf.x1iyjqo2.x6ikm8r.x10wlt62.x1n2onr6.x1xzczws',
        sendButtonElSelector:
          'div[class="x1i10hfl xjqpnuy xa49m3k xqeqjp1 x2hbi6w xdl72j9 x2lah0s xe8uvvx xdj266r x1mh8g0r x2lwn1j xeuugli x1hl2dhg xggy1nq x1ja2u2z x1t137rt x1q0g3np x1lku1pv x1a2a7pz x6s0dn4 xjyslct x1ejq31n xd10rxx x1sy0etr x17r0tee x9f619 x1ypdohk x1f6kntn xwhw2v2 xl56j7k x17ydfre x2b8uid xlyipyv x87ps6o x14atkfc xcdnw81 x1i0vuye xjbqb8w xm3z3ea x1x8b98j x131883w x16mih1h x972fbf xcfux6l x1qhh985 xm0m39n xt0psk2 xt7dq6l xexx8yu x4uap5 x18d9i69 xkhd6sd x1n2onr6 x1n5bzlp x173jzuc x1yc6y37 x7fd4wk x1sfzahb xfs2ol5"]',
        inputElSelector:
          'div.xzsf02u.x1a2a7pz.x1n2onr6.x14wi4xw.x1iyjqo2.x1gh3ibb.xisnujt.xeuugli.x1odjw0f.notranslate',
        messageListElSelector:
          'div.x1uipg7g.xu3j5b3.xol2nv.xlauuyb.x26u7qi.x19p7ews.x78zum5.xdt5ytf.x1iyjqo2.x6ikm8r.x10wlt62',
        reciveMessageElSelector:
          'div.x1uipg7g.xu3j5b3.xol2nv.xlauuyb.x26u7qi.x19p7ews.x78zum5.xdt5ytf.x1iyjqo2.x6ikm8r.x10wlt62 span.x193iq5w.xeuugli.x13faqbe.x1vvkbs.x1xmvt09.x1lliihq.x1s928wv.xhkezso.x1gmr53x.x1cpjm7i.x1fgarty.x1943h6x.xudqn12.x3x7a5m.x6prxxf.xvq8zen.xo1l8bm.xzsf02u',
        sendMessageElSelector:
          'div.x1uipg7g.xu3j5b3.xol2nv.xlauuyb.x26u7qi.x19p7ews.x78zum5.xdt5ytf.x1iyjqo2.x6ikm8r.x10wlt62 span.x193iq5w.xeuugli.x13faqbe.x1vvkbs.x1xmvt09.x1lliihq.x1s928wv.xhkezso.x1gmr53x.x1cpjm7i.x1fgarty.x1943h6x.xudqn12.x3x7a5m.x6prxxf.xvq8zen.xo1l8bm.x14ctfv'
      },
      telegram: {
        messageListElSelector: '.has-sticky-dates',
        // 自己发送的
        reciveMessageElSelector: '.bubble.is-out .message.spoilers-container',
        // 对方发送的
        sendMessageElSelector: '.bubble.is-in .message.spoilers-container .translatable-message',
        // 当前active的聊天人
        activeChat: 'a.chatlist-chat.active',
        // 聊天列表
        chatList: 'ul.chatlist a.chatlist-chat',
        // 滚动区域
        scrollContent: '.bubbles.has-groups.has-sticky-dates .scrollable.scrollable-y',
        // 输入框
        input: '.input-message-container [contenteditable="true"]',
        inputContent: '.input-message-container [contenteditable="true"]',
        // 发送按钮
        sendBtn: '.btn-send-container button',
        // 发送按钮遮罩层父级
        maskDependEl: '.btn-send-container',
        // 新粉丝名
        newFansNickName: '[data-only-first-name="0"]'
      },
      whatsapp: {
        searchListSelector: 'span[class="x10l6tqk x13vifvy xtijo5x x1ey2m1c x1o0tod"]',
        chatListContainerSelector: '#pane-side',
        scrollElSelector:
          "[class='x10l6tqk x13vifvy x1o0tod xyw6214 x9f619 x78zum5 xdt5ytf xh8yej3 x5yr21d x6ikm8r x1rife3k xjbqb8w x1ewm37j']",
        dividerElSelector:
          "hr[class='xvy4d1p xjm9jq1 xamhcws x13fuv20 xxdxl9i x18oe1m7 x1sy0etr xstzfhl xw7yly9']",
        // 用户名
        nickNameElSelector: '.x1iyjqo2.x6ikm8r.x10wlt62.x1n2onr6.xlyipyv.x1fj9vlw._ao3e',
        shopOwnersNickNameElSelector:
          "div[class='xs83m0k x1g77sc7 xeuugli x2lwn1j xozqiw3 x1oa3qoh x12fk4p8 x1iyjqo2 x1t1x2f9 x6ikm8r x10wlt62 x37zpob xg83lxy']",
        // 消息列表
        messageListElSelector: '#main [data-tab="8"]',
        // 自己发送的
        reciveMessageElSelector: '.message-out ._akbu span._ao3e',
        // 对方发送的
        sendMessageElSelector: '.message-in ._akbu span._ao3e',
        // 聊天列表
        chatList: '[role="grid"] [role="listitem"]',
        // 未读消息
        unread: '[aria-label*="条未读消息"]',
        // 未读消息phone
        unreadPhone: '[aria-colindex="2"] div._ak8q',
        // 滚动区域
        scrollContent:
          "[class='x10l6tqk x13vifvy x1o0tod xyw6214 x9f619 x78zum5 xdt5ytf xh8yej3 x5yr21d x6ikm8r x1rife3k xjbqb8w x1ewm37j']",
        // 在localStorage获取WS账号信息的key
        userInfoKey: 'last-wid-md',
        // 当前在选中状态的聊天对象
        activeChat: '[aria-selected="true"]',
        activeChatId: '[aria-selected="true"] [aria-colindex="2"] span[dir="auto"]',
        // 当前聊天是个人
        personalChat: '[role="grid"] [aria-selected="true"] [aria-colindex="2"]',
        // 是频道
        channelChat: '[aria-label="频道列表"] [aria-selected="true"] [aria-colindex="2"]',
        // 发送按钮
        sendBtn: '[data-icon="wds-ic-send-filled"]',
        sendBtnBusiness: '#main button>span[data-icon="send"]',
        // 发送按钮遮罩层父级
        maskDependIsMerchantEl: '._ak1q div.x123j3cw',
        // 发送按钮遮罩层父级 非商户
        maskDependEl:
          '[class="x9f619 x78zum5 x6s0dn4 xl56j7k xpvyfi4 x2lah0s x1c4vz4f x1fns5xo x1ba4aug x14yjl9h xudhj91 x18nykt9 xww2gxu x1pse0pq x8j4wrb"]',
        // 输入框
        input: '[tabindex="10"]',
        inputContent: 'div[tabindex="10"] span[data-lexical-text="true"]',
        // 附件发送按钮
        attSendBtn: '[aria-label="发送"]',
        // 附件发送按钮遮罩层父级
        attMaskDependEl: 'div.x1247r65',
        // 附件输入框
        attInput: '[aria-placeholder="添加说明"]',
        attInputContent: '[aria-placeholder="添加说明"] span[data-lexical-text="true"]'
      },
      tiktok: {
        userIdElSelector: 'a[data-e2e="nav-profile"]',
        inputElSelector: '[class="notranslate public-DraftEditor-content"]',
        inputTextElSelector:
          '[class="public-DraftStyleDefault-block public-DraftStyleDefault-ltr"]>span>span',
        currentSessionElSelector: "[class='css-1ojajeq-DivItemWrapper eii3f6d3']",
        sessionUserNameElSelector: "p[class='css-1l1cwdw-PInfoNickname eii3f6d9']",
        messageListElSelector: "[class='css-okl125-DivChatMainContent ediam1h26']",
        unreadElSelector: "[class='css-1xdqxu2-SpanNewMessage eii3f6d2']",
        sessionElSelector: '[class="css-1rrx3i5-DivItemWrapper eii3f6d3"]',
        sendButtonElSelector: '[class="css-d7yhdo-StyledSendButton e1823izs3"]',
        sendButtonContainerElSelector: `[class="css-z1eu53-DivMessageInputAndSendButton e1823izs1"]`,
        reciveMessageElSelector:
          '[class="css-1e0nj4n-DivMessageHorizontalContainer e9j91388"] p[class="css-1rdxtjl-PText e9j913815"]',
        sendMessageElSelector:
          '[class="css-wvg5vm-DivMessageHorizontalContainer e9j91388"] p[class="css-1rdxtjl-PText e9j913815"]'
      },
      facebookBusiness: {
        userNameSelector: '[class="x78zum5 x2lwn1j xeuugli xh8yej3"]',
        inputElSelector: 'textarea[type="text"]',
        sessionUserNameElSelector: '[class="x78zum5 x1q0g3np x2lwn1j xeuugli"]',
        currentSessionUserNameElSelector: "[class='x1swdo50 xw2csxc x1odjw0f _5bpf']",
        messageListElSelector: "[class='x1yrsyyn x5zjp28 x10b6aqq xwn43p0']",
        unreadElSelector: '._5m10._284c ._5hhj',
        sessionElSelector: '[class="xh8yej3 x2izyaf"] [role="presentation"]',

        sendButtonElSelector: '[class="xogfrqt x1jchvi3"]>div',
        sendButtonContainerElSelector: `[class="x6s0dn4 x78zum5 x2lwn1j xeuugli"]`,
        reciveMessageElSelector:
          '[class="xuk3077 x78zum5 x1q0g3np x2lwn1j xeuugli x193iq5w xh8yej3"] div[class="x1rg5ohu x67bb7w"]>div>span:first-child',

        sendMessageElSelector:
          '[class="xuk3077 x78zum5 x1q0g3np x13a6bvl x2lwn1j xeuugli x1f6kntn xlxfd2w x1gslohp x126k92a"] div[class="x1rg5ohu x67bb7w"]>div>span:first-child'
      },
      twitter: {
        userElSelector: 'nav>a:last-of-type',
        inputElSelector: 'div.DraftEditor-editorContainer>div',
        inputTextElSelector: 'div.public-DraftStyleDefault-block>span[data-offset-key]>span',

        sessionUserNameElSelector:
          'div[class="css-146c3p1 r-dnmrzs r-1udh08x r-1udbk01 r-3s2u2q r-bcqeeo r-1ttztb7 r-qvutc0 r-37j5jr r-a023e6 r-rjixqe r-16dba41 r-18u37iz r-1wvb978"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',
        currentSessionUserNameElSelector: `.r-x572qd div[class="css-146c3p1 r-dnmrzs r-1udh08x r-1udbk01 r-3s2u2q r-bcqeeo r-1ttztb7 r-qvutc0 r-37j5jr r-a023e6 r-rjixqe r-16dba41 r-18u37iz r-1wvb978"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]`,
        messageListElSelector:
          "[class='css-175oi2r r-16y2uox r-10m9thr r-1h0z5md r-f8sm7e r-13qz1uu r-3pj75a r-1ye8kvj']",

        unreadElSelector: '.r-l5o3uw',
        sessionElSelector: 'div[role="tablist"]',
        sendButtonElSelector:
          'button[class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1ez5h0i r-2yi16 r-1qi8awa r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l"]',
        sendButtonContainerElSelector: `[class="css-175oi2r r-1awozwy r-1sw30gj r-1867qdf r-18u37iz r-l00any r-jusfrs r-tuq35u r-1h0ofqe"]`,
        reciveMessageElSelector:
          'div[class="css-175oi2r r-1habvwh r-1wbh5a2"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',

        sendMessageElSelector:
          'div[class="css-175oi2r r-obd0qt r-1wbh5a2"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]'
      }
    }
    if (!app.isPackaged) {
      GlobalObject.selectorInfo = r
    } else {
      if (res.code === 1) {
        GlobalObject.selectorInfo = res.data
      }
    }
  }

  async function init() {
    GlobalObject.translateDB = new TranslateDB({
      fileName: path.join(app.getPath('userData'), import.meta.env.MODE, 'translate.db.json')
    })
    GlobalObject.mainWindow = createWindow()

    // const ww = new BaseWindow({
    //   show: false,
    //   height: 100,
    //   width: 200
    // })

    // let wv = new WebContentsView()
    // wv.webContents.loadURL('https://www.zhihu.com/tardis/zm/art/685475824')
    // wv.webContents.openDevTools()
    // ww.contentView.addChildView(wv)

    // 添加验证
    if (!GlobalObject.mainWindow) {
      console.error('Failed to create main window')
      return
    }

    GlobalObject.messageManager = new MessageManager()
    GlobalObject.viewManager = new ViewManager()
    initShortcut()
  }
  //新版本= end======================
  const gotTheLock = app.requestSingleInstanceLock()
  if (!gotTheLock) {
    app.quit() // 如果没有获取到锁，则退出当前实例
  } else {
    app.on('second-instance', (event, commandLine, workingDirectory) => {
      // 当尝试打开第二个实例时，聚焦到第一个实例的窗口
      if (GlobalObject.mainWindow) {
        if (GlobalObject.mainWindow.isMinimized()) mainWindow.restore()
        GlobalObject.mainWindow.focus()
        GlobalObject.mainWindow.show() // 点击托盘图标时显示主窗口
      }
    })
  }

  // This method will be called when Electron has finished
  // initialization and is ready to create browser windows.
  // Some APIs can only be used after this event occurs.
  app.whenReady().then(async () => {
    if (platform.isMacOS) {
      try {
        const status = systemPreferences.getMediaAccessStatus('microphone')
        if (status === 'not-determined') {
          await systemPreferences.askForMediaAccess('microphone')
        }
      } catch (error) {
        console.error('Failed to request microphone permission:', error)
      }
    }
    app.commandLine.appendSwitch('wm-window-animations-disabled')
    app.on('browser-window-created', (_, window) => {
      optimizer.watchWindowShortcuts(window)
    })

    mountIpcApi()
    await init()

    // autoUpdater
    //   .checkForUpdates()
    //   .then((info) => {
    //     if (info.updateInfo) {
    //       console.log('new version:', info.updateInfo)
    //       // 发现有新版本，可以提示用户是否下载更新
    //     } else {
    //       console.log('当前已是最新版本')
    //       // 没有新版本，可以提示用户当前已是最新版本
    //     }
    //   })
    //   .catch((error) => {
    //     console.error('update error:', error)
    //   })
    let ai_open_num = 0
    // 监听来自渲染进程的打开新窗口的请求
    ipcMain.on('open-new-window', (event, url) => {
      if (ai_open_num == 0) {
        ai_open_num = 1
        const iconPath = join(__dirname, '../../ai.png')
        const childWindow = new BrowserWindow({
          width: 900,
          height: 600,
          icon: iconPath,
          autoHideMenuBar: true,
          parent: GlobalObject.mainWindow, // 使子窗口依赖于主窗口
          // modal: true, // 模态窗口，关闭主窗口时会关闭子窗口
          webPreferences: { nodeIntegration: false }
        })
        childWindow.setTitle('蓝海AI')
        childWindow.loadURL(url) // 子窗口的 Vue 页面
        // 设置应用的标题
        childWindow.webContents.on('did-finish-load', () => {
          childWindow.setTitle('蓝海AI')
        })
        childWindow.on('closed', () => {
          ai_open_num = 0
        })
      }
    })

    // Set app user model id for windows
    electronApp.setAppUserModelId('com.lanhaiyitong')

    // Default open or close DevTools by F12 in development
    // and ignore CommandOrControl + R in production.
    // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
    // 开发环境启用f12 打开开发者工具， 生产环境禁用

    let accessToken = ''

    // 接收前端传来的 token
    ipcMain.on('getUserLoginToken', (event, { token, uuid }) => {
      accessToken = token
      queryParams.uuid = uuid
      getDatasetRolePrompt({ dataset: uuid })
      getSelectorInfo(accessToken)
    })

    // 获取翻译配置为空的时候 重新获取
    ipcMain.on('empty-translate-config', (event, arg) => {
      GlobalObject.mainWindow.webContents.send('empty-translate-config-to-vue', arg)
    })
    //  -- 添加粉丝
    ipcMain.on('sendFansList', (event, arg) => {
      GlobalObject.mainWindow.webContents.send('sendFansList', arg)
    })
    // 获取粉丝会话列表--监听预加载进程发来消息--ws
    ipcMain.on('sendWhatsFansList', (event, arg) => {
      BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          // 发送消息给渲染层vue
          win.webContents.send('getWhatsFansList', arg)
        }
      })
    })
    // WS发送当前打开会话用户ID
    ipcMain.on('sendWsActiveFanId', (event, arg) => {
      BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send('sendWsActiveFanId', arg)
        }
      })
    })

    // zalo发送当前打开会话用户ID
    ipcMain.on('sendZaloActiveFanId', (event, arg) => {
      BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send('sendZaloActiveFanId', arg)
        }
      })
    })
    // ins发送当前打开会话用户ID
    ipcMain.on('sendInsActiveFanId', (event, arg) => {
      BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send('sendInsActiveFanId', arg)
        }
      })
    })
    // FB发送当前打开会话用户ID
    ipcMain.on('sendFbActiveFanId', (event, arg) => {
      GlobalObject.mainWindow.webContents.send('sendFbActiveFanId', arg)
    })
    // tg发送当前打开会话用户ID
    ipcMain.on('sendActiveFanId', (event, arg) => {
      BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send('getActiveFanId', arg)
        }
      })
    })
    // 多部执行 系统翻译内容
    ipcMain.on('receive_trans_info', async (event, inputText, index, sonIndex, in_out) => {
      const res = await getTranslate(JSON.parse(inputText), accessToken)
      // 翻译完成并执行了复制黏贴 回复消息给预加载层
      setTimeout(function () {
        event.reply(
          'translate-over-in',
          JSON.stringify(res),
          index,
          sonIndex,
          in_out,
          JSON.parse(inputText).text
        )
      }, 50)
    })
    // 点击翻译或回车翻译
    ipcMain.on('click_trans_info', async (event, inputText) => {
      const res = await getTranslate(JSON.parse(inputText), accessToken)
      // 翻译完成并执行了复制黏贴 回复消息给预加载层
      setTimeout(function () {
        event.reply('translate-over-click', JSON.stringify(res))
      }, 50)
    })
    // 本地存储  聊天数据
    let localChatLog = ''
    ipcMain.on('getLocalChatLog', (event, inputText) => {
      localChatLog = JSON.parse(inputText)
    })
    // 保存聊天记录
    ipcMain.on('getSaveChatLog', async (event, message) => {
      let chatHistoryArray = JSON.parse(message)
      console.log(chatHistoryArray, 'get_infoget_infoget_info')
      const chatHistoryData = chatHistoryArray.map((i) => ({
        accountId: i.ccountId,
        fansId: i.fansId,
        chatType: i.type == 'out' ? 1 : 2,
        originalText: i.text,
        translateText: i.translateText,
        sendTime: i.time,
        toLang: i.toLang,
        platform: i.platform,
        fromLang: i.fromLang || 'auto'
      }))
      await saveChat(chatHistoryData, accessToken)
    })
    // 系统翻译信息
    ipcMain.handle('getMessageTranslate', async (event, message) => {
      try {
        let get_info = JSON.parse(message)
        console.log('main', message)
        let translateInfo = {
          text: get_info.text,
          engineCode: get_info.engineCode,
          fromLang: get_info.fromLang,
          toLang: get_info.toLang
        }
        let res = {}
        if (get_info.isInput && get_info.toLang === get_info.recive_lang) {
          res = { code: 1, data: { result: get_info.text } }
        } else {
          res = await getTranslate(translateInfo, accessToken)
          console.log('翻译结果%o \n', res)
          if (res.code == 400010) {
            GlobalObject.mainWindow.send('noMoney', res.msg)
          }
        }

        // res.data.result
        return res
        // return {}
      } catch (error) {
        // 处理异常
        console.error('翻译内容报错', error)
        return {}
      }
    })

    // 监听是否有新粉丝进入----tg接口
    ipcMain.on('newFansAdd', (event, arg) => {
      BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send('newFansInsert', arg)
        }
      })
    })

    ipcMain.on('clearS', (event, arg) => {
      clearAllCredentials()
    })

    app.on('activate', function () {
      if (BrowserWindow.getAllWindows().length === 0) {
        GlobalObject.mainWindow = createWindow()
      } else {
        // 确保 GlobalObject.mainWindow 指向正确的窗口
        if (!GlobalObject.mainWindow || GlobalObject.mainWindow.isDestroyed()) {
          const windows = BrowserWindow.getAllWindows()
          GlobalObject.mainWindow = windows[0] // 获取第一个窗口
        }
      }
    })
  })

  // Quit when all windows are closed, except on macOS. There, it's common
  // for applications and their menu bar to stay active until the user quits
  // explicitly with Cmd + Q.
  // app.on('window-all-closed', () => {
  //   if (process.platform !== 'darwin') {
  //     app.quit()
  //   }
  // })

  async function clearAllCredentials() {
    // 获取默认会话和所有 WebView 的会话
    const defaultSession = session.defaultSession
    const allSessions = [defaultSession]

    // 查找所有 WebView 的会话
    const webViews = getAllWebViews() // 需要实现这个函数获取所有 WebView 实例
    webViews.forEach((webView) => {
      if (webView.getWebContents && !allSessions.includes(webView.getWebContents().session)) {
        allSessions.push(webView.getWebContents().session)

        GlobalObject.mainWindow.contentView.removeChildView(view)
        GlobalObject.viewManager.deleteView(id)
        if (!webView.isDestroyed()) {
          webView.close()
        }
      }
    })

    // 清除每个会话的数据
    for (const ses of allSessions) {
      try {
        // 清除 cookies
        await ses.clearStorageData({
          storages: [
            'cookies',
            'localstorage',
            'shadercache',
            'websql',
            'serviceworkers',
            'cachestorage'
          ],
          quotas: ['temporary', 'persistent', 'syncable']
        })

        // 清除 HTTP 认证缓存
        await ses.clearAuthCache()

        // 清除主机解析缓存
        await ses.clearHostResolverCache()

        // 清除缓存
        await ses.clearCache()
      } catch (error) {
        console.error(`清除会话数据失败: ${error.message}`)
      }
    }
  }

  // 示例：获取所有 WebView 实例的函数
  function getAllWebViews() {
    const webViews = []

    // 获取所有 BrowserWindow
    const windows = BrowserWindow.getAllWindows()

    // 遍历所有窗口查找 WebView
    windows.forEach((win) => {
      if (win.webContents) {
        // 查找窗口中的 WebView
        const webContents = win.webContents
        if (webContents && webContents.debugger && webContents.getWebRTCIPHandlingPolicy) {
          // 这是主窗口的 webContents
          // 可以进一步查找嵌套的 WebView
          // 这里需要根据你的实际应用结构来实现
        }
      }
    })

    return webViews
  }
  // In this file you can include the rest of your app"s specific main process
  // code. You can also put them in separate files and require them here.
} catch (error) {
  console.log(error)
}
