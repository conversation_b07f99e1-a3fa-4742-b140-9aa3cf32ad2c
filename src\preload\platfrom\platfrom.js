import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { Translater } from '../tanslate/index'
import { createDefaultSpoofer } from '../utils/fingerprint.js'
import { throttle, OptimizedMutationObserver, createPerformanceMonitor, BatchDOMUpdater } from '../utils/performance.js'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import TranslateContrast from '../tanslate/translateContrast'
dayjs.extend(isBetween)

/**
 * 判断当前时间是否在任意时间段内（仅比较当天的时分秒）
 * @param ranges 时间段数组，例如 [["00:00:00", "00:48:02"], ["12:00:00", "13:30:00"]]
 * @returns 是否在任意时间段内
 */
export function isNowInRange(ranges) {
  const now = dayjs()
  const today = dayjs().format('YYYY-MM-DD')

  return ranges.some(([startStr, endStr]) => {
    const start = dayjs(`${today} ${startStr}`)
    const end = dayjs(`${today} ${endStr}`)
    return now.isBetween(start, end, null, '[)')
  })
}
export class Platform {
  constructor(platform) {
    this.platform = platform
    this.performanceMonitor = createPerformanceMonitor(`Platform-${platform}`)
    this.domUpdater = new BatchDOMUpdater()
  }

  platform = null
  mutationObserver = null
  pathObserver = null
  viewSessionId = null
  sI = null
  isAutoAiReplying = false
  aiReplyConfig = null
  fingerprintSpoofer = null
  performanceMonitor = null
  domUpdater = null

  createTranslateContrast() {
    this.translateContrast = new TranslateContrast()
    console.log(this.translateContrast)
  }

  roundView(r) {
    if (!r) {
      setTimeout(() => {
        this.roundView(document.querySelector('#app'))
      }, 200)
      return
    }
    document.documentElement.style.setProperty('background', 'transparent', 'important')
    document.body.style.setProperty('background', 'transparent', 'important')

    let style = {
      'border-radius': '10px',
      overflow: 'hidden'
    }
    Object.keys(style).forEach((k) => {
      r.style.setProperty(k, style[k], 'important')
    })
  }

  async updateAiReplyConfig() {
    ipcRenderer.on('updateAiReplyConfig', (_event, arg) => {
      this.aiReplyConfig = arg
    })
    this.aiReplyConfig = await ipcRenderer.invoke('getAiReplyConfig')
  }

  /**
   * 初始化指纹伪造
   * 使用模块化的指纹伪造器
   */
  initFingerprinting() {
    if (!this.fingerprintSpoofer) {
      this.fingerprintSpoofer = createDefaultSpoofer()
      this.fingerprintSpoofer.init()
      console.log(`[${this.platform}] Fingerprint spoofing initialized`)
    }
  }

  initContextMenu() {
    let menuElement = document.createElement('div')
    let copy_text = ''

    const menuItems = [
      {
        class: 'menu-item',
        id: 'copy-option',
        textContent: '📋 复制',
        tag: 'div',
        onClick: async (e) => {
          e.preventDefault()
          if (copy_text) {
            try {
              await navigator.clipboard.writeText(copy_text)
              e.target.innerText = '📋 已复制'
              setTimeout(() => {
                menuElement.style.display = 'none'
                e.target.innerText = '📋 复制'
                window.getSelection().removeAllRanges()
              }, 500)
            } catch (err) {
              console.error('❌ 复制失败: ' + err.message)
            }
          }
        }
      }
    ]
    const menuElList = []

    menuItems.forEach((item) => {
      const el = document.createElement(item.tag)
      el.id = item.id
      el.className = item.class
      el.textContent = item.textContent
      el.onclick = item.onClick
      menuElList.push(el)
    })
    menuElement.id = 'custom-menu'
    menuElement.append(...menuElList)
    document.body.append(menuElement)

    document.addEventListener('mouseup', (event) => {
      const selection = window.getSelection()
      if (!selection && !selection.focusNode) return
      const input = document.querySelector(this.sI.input || this.sI.inputElSelector)
      if (input && input.contains(selection.focusNode)) {
        return
      }
      setTimeout(() => {
        const selectedText = selection.toString().trim()
        if (selectedText) {
          const info = {
            x: event.clientX,
            y: event.clientY
          }

          const x = info.x + 110 > document.body.clientWidth ? info.x - 110 : info.x
          const y = info.y + 110 > document.body.clientWidth ? info.y - 110 : info.y
          menuElement.style.top = `${y}px`
          menuElement.style.left = `${x}px`
          menuElement.style.display = 'block'
          copy_text = selectedText
        }
      }, 0)
    })
    let isClickingMenu = false

    // 标记点击菜单
    menuElement.addEventListener('mousedown', () => {
      isClickingMenu = true
    })

    document.addEventListener('mousedown', (e) => {
      if (!menuElement.contains(e.target)) {
        isClickingMenu = false
      }
    })

    document.addEventListener('click', (e) => {
      if (!isClickingMenu && !menuElement.contains(e.target)) {
        menuElement.style.display = 'none'
      }
    })
  }

  async getSelectorInfo() {
    this.sI = await ipcRenderer.invoke('getSelectorInfo', {
      platform: this.platform
    })
    console.log('SelectorInfo: %O\n', this.sI)
  }

  /**
   * 初始化平台处理器
   * 优化初始化流程，提高性能
   */
  async init(translater) {
    this.performanceMonitor.mark('platform-init-start')

    if (!this.platform) {
      console.warn('[Platform] platform is null')
      alert('无法识别的平台，请联系管理员')
      return
    }

    try {
      // 并行执行不依赖 DOM 的初始化
      const [viewSessionId, selectorInfo] = await Promise.all([
        this.getViewSessionId(),
        this.getSelectorInfo()
      ])

      this.performanceMonitor.mark('async-init-complete')

      // 初始化指纹伪造
      this.initFingerprinting()

      // 初始化翻译器
      if (translater instanceof Translater) {
        this.translater = translater
        this.translater.init(this)
      }

      // DOM 相关初始化
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          this.initDOMRelatedFeatures()
          this.createTranslateContrast()
        })
      } else {
        // DOM 已经加载完成
        this.initDOMRelatedFeatures()
      }
      if (this.platform === 'whatsapp') {
        this.roundView(document.querySelector('#app'))
      }

      // 异步初始化 AI 回复配置
      this.updateAiReplyConfig().catch((error) => {
        console.error('[Platform] Failed to update AI reply config:', error)
      })

      this.performanceMonitor.mark('platform-init-end')
      console.log(`[${this.platform}] Platform initialized successfully`)
    } catch (error) {
      console.error(`[${this.platform}] Platform initialization failed:`, error)
      throw error
    }
  }

  /**
   * 初始化 DOM 相关功能
   */
  async initDOMRelatedFeatures() {
    this.performanceMonitor.mark('dom-init-start')

    try {
      await this.initViewFunc()
      this.initObserver()
      this.initContextMenu()
      this.initEventHandler()

      this.performanceMonitor.mark('dom-init-end')
      console.log(`[${this.platform}] DOM features initialized`)
    } catch (error) {
      console.error(`[${this.platform}] DOM initialization failed:`, error)
    }
  }

  async getViewSessionId() {
    this.viewSessionId = await ipcRenderer.invoke('getViewSessionId')
    console.log('viewSessionId: %s\n', this.viewSessionId)
  }

  /**
   * 初始化优化的观察器
   */
  initObserver() {
    this.performanceMonitor.mark('observer-init-start')

    // 使用优化的观察器
    this.mutationObserver = new OptimizedMutationObserver(this._o.bind(this), {
      throttleDelay: 200,
      batchSize: 10
    })

    this.mutationObserver.observe(document.body, {
      subtree: true,
      childList: true,
      // 优化配置，减少不必要的观察
      attributes: false,
      characterData: false
    })

    this.performanceMonitor.mark('observer-init-end')
    console.log(`[${this.platform}] Optimized observer initialized`)
  }

  /**
   * 处理 DOM 变更
   * 子类应该重写此方法
   */
  _o(mutations, observer) {
    // 子类实现具体逻辑
  }

  /**
   * 处理 URL 变更
   * 子类应该重写此方法
   */
  _u() {
    // 子类实现具体逻辑
  }

  sendMessageToInput(...args) {
    console.log('sendMessageToInput', ...args)
  }
  autoReplyHandler() {
    console.log('autoReplyHandler')
  }

  async clickAt(params) {
    return await ipcRenderer.invoke('clickAt', params)
  }
  async aiReply(callback) {
    // && isNowInRange([this.aiReplyConfig.aiTimeRang])
    try {
      return new Promise(async (resovle) => {
        if (!this.isAutoAiReplying && this.aiReplyConfig.ai) {
          this.isAutoAiReplying = true
          await callback()
          console.log('aiReply')
          this.isAutoAiReplying = false
          resovle()
        }
      })
    } catch (error) {
      this.isAutoAiReplying = false
      console.log(error)
    }
  }

  translateList() {}
  async getAiReply(params) {
    try {
      let v = await ipcRenderer.invoke('aiAutoReply', params)
      return v
    } catch (error) {
      return
    }
  }

  onUserLoginOut() {
    ipcRenderer.on('onUserLoginOut ', () => {
      localStorage.clear()
      sessionStorage.clear()
      cookieStore.getAll().then((res) => {
        res.forEach((key) => {
          cookieStore.delete(key)
        })
      })
    })
  }

  async initViewFunc() {
    this.onUserLoginOut()
    let r = await ipcRenderer.invoke('initViewFunc')
    if (r) {
      // 监听hash
      window.addEventListener('hashchange', () => {
        this._u(window.location)
      })
      // 监听 popstate 事件
      window.addEventListener('popstate', () => {
        this._u(window.location)
      })

      // 监听 pushstate 事件
      window.addEventListener('pushstate', () => {
        this._u(window.location)
        console.log('使用 pushState 改变了 URL:', window.location.href)
      })

      // 监听 replacestate 事件
      window.addEventListener('replacestate', () => {
        this._u(window.location)
        console.log('使用 replaceState 改变了 URL:', window.location.href)
      })
      return true
    } else {
      return false
    }
  }

  sendPlatformUserInfo(params) {
    try {
      if (ipcRenderer && typeof ipcRenderer.send === 'function') {
        if (params.userId && params.userId !== '0') {
          ipcRenderer.send('sendUserInfoViewToMain', params)
        }
      }
    } catch (error) {
      console.error('Failed to send platform user info:', error)
    }
  }

  _sendUnReadCount(unReadCount) {
    try {
      if (ipcRenderer && typeof ipcRenderer.send === 'function') {
        ipcRenderer.send('setUnReadCount', { platform: this.platform, unReadCount })
      }
    } catch (error) {
      console.error('Failed to send unread count:', error)
    }
  }

  sendNewFansList(params) {
    ipcRenderer.send('addNewFansList', params)
  }

  sendUnReadCount = throttle(this._sendUnReadCount, 2000, { trailing: true })

  sendCurrentSessionFansInfo(params) {
    ipcRenderer.send('sendActiveFansIdViewToMain', params)
  }

  initEventHandler() {
    ipcRenderer.on('onMessageMainToView', (_event, ...args) => {
      this.sendMessageToInput(...args)
    })
  }

  onVisibilityChange() {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        this.translateList()
      }
    })
  }
}


const hostNameMap = new Map([
  ['www.facebook.com', 'facebook'],
  ['whatsapp', 'whatsapp'],
  ['telegram', 'telegram'],
  ['instagram', 'instagram'],
  ['tiktok', 'tiktok'],
  ['business.facebook.com', 'facebookBusiness'],
  ['x.com', 'twitter'],
  ['discord.com', 'discord']
])

export const getPlatform = () => {
  let host = JSON.parse(import.meta.env.VITE_PLATFROM_HOST_NAMES).find((name) => {
    if (window.location.host.match(name)) {
      return true
    } else {
      return false
    }
  })

  return hostNameMap.get(host)
}


