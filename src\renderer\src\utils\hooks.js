import { onMounted, onBeforeUnmount, ref } from 'vue';
import { debounce } from 'lodash-es';
import { getTargetElement } from 'vue-hooks-plus/lib/utils/domTarget';
export const useChatBounds = (
  option,
  duration = 100
) => {
  const m = ref(false)
  const onWebContentViewResize = debounce((entries) => {
    for (const entry of entries) {
      const target = entry.target;
      const rect = target.getBoundingClientRect();

      const bounds = {
        x: rect.left,
        y: rect.top,
        width: rect.width,
        height: rect.height,
      };
      if (bounds.width > 0 && bounds.height > 0 && m && m.value) {
        option.callback(bounds);
      }
    }
  }, duration);
  const resizeObserver = new ResizeObserver(onWebContentViewResize);

  onMounted(() => {
    m.value = true
    const element = getTargetElement(option.targeteElement);
    if (element) {
      resizeObserver.observe(element, option.targeteElementResizeObserverOption);
      const initialRect = element.getBoundingClientRect();
      const initialBounds = {
        x: initialRect.left,
        y: initialRect.top,
        width: initialRect.width,
        height: initialRect.height,
      };
      if (initialBounds.width > 0 && initialBounds.height > 0) {
        option.callback(initialBounds);
      }
    }
  });

  onBeforeUnmount(() => {
    m.value = false
    onWebContentViewResize.cancel();
    resizeObserver.disconnect();
  });
  return {
    resizeObserver,
  };
};



export function useEscKey(callback, delay = 500) {
  let cooldown = false

  const handler = (event) => {
    if (
      event.key === 'Escape' &&
      !event.ctrlKey &&
      !event.shiftKey &&
      !event.altKey &&
      !event.metaKey &&
      !cooldown
    ) {
      cooldown = true
      callback()
      setTimeout(() => (cooldown = false), delay)
    }
  }

  onMounted(() => {
    document.addEventListener('keydown', handler)
  })

  onBeforeUnmount(() => {
    document.removeEventListener('keydown', handler)
  })
}
