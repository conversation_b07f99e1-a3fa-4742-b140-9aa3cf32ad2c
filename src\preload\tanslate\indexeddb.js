// let databaseName = 'chatLogSel'
// let databaseVersion = 7
// let storeName = 'messages'
// // 创建聊天库 indexDB
// const createIndexDB = () => {
//   let request = indexedDB.open(databaseName, databaseVersion)
//   // 新建数据库
//   request.onupgradeneeded = function (e) {
//     const db = e.target.result
//     // 通常新建数据库以后，第一件事是新建对象仓库（即新建表），并设置主键
//     var objectStore = db.createObjectStore(storeName, {
//       keyPath: 'id' //设置主键为 id
//     })
//     db.close()
//   }

//   request.onerror = function (event) {}
//   request.onsuccess = function (event) {}
// }
// createIndexDB()

// // 查询indexDb数据库是否有翻译数据
// const getTranslateIndexDBDate = (translateData) => {
//   console.log('get', translateData)

//   return new Promise((resolve, reject) => {
//     let databaseName = 'chatLogSel'
//     let databaseVersion = 7
//     let storeName = 'messages'
//     let request = indexedDB.open(databaseName, databaseVersion)

//     request.onerror = function (event) {
//       reject('Database error: ' + event.target.errorCode)
//     }

//     request.onsuccess = function (event) {
//       try {
//         // 链接库
//         const db = event.target.result
//         var transaction = db.transaction([storeName], 'readwrite')
//         var objectStore = transaction.objectStore(storeName)
//         let indexDb_id = unescape(encodeURIComponent(translateData.text + translateData.toLang))
//         const messageLog = objectStore.get(btoa(indexDb_id)) // 获取指定ID的用户数据
//         messageLog.onerror = function (event) {
//           // console.error("Transaction failed: " + event.target.errorCode);
//         }
//         messageLog.onsuccess = function (event) {
//           if (messageLog.result) {
//             // console.log("User: indexDB 翻译", messageLog.result); // 打印用户数据
//             resolve(messageLog.result)
//           } else {
//             // console.log("No such user"); // 如果没有找到用户，打印消息
//             resolve()
//           }
//         }
//         db.close()
//       } catch (error) {
//         console.error('Error:', error)
//       }
//     }
//   })
// }

// // 添加翻译数据到indexDB数据库
// const addTranslateIndexDb = (translateData) => {
//   console.log('add', translateData)

//   let databaseName = 'chatLogSel'
//   let databaseVersion = 7
//   let storeName = 'messages'
//   let request = indexedDB.open(databaseName, databaseVersion)

//   request.onsuccess = async function (event) {
//     try {
//       const db = event.target.result
//       var transaction = db.transaction([storeName], 'readwrite')
//       var objectStore = transaction.objectStore(storeName)
//       let indexDb_id = unescape(encodeURIComponent(translateData.text + translateData.toLang))

//       var newItem = {
//         id: btoa(indexDb_id),
//         value: translateData.transText
//       }

//       if (!(await objectStore.get(newItem.id))) {
//         var addRequest = objectStore.add(newItem)
//         addRequest.onsuccess = function (event) {
//           // console.log("Item added successfully");
//         }
//         addRequest.onerror = function (event) {
//           console.warn('Error adding item: ', event)
//         }
//       } else {
//         objectStore.put(newItem)
//       }

//       if (translateData.recive_lang !== translateData.toLang) {
//         let indexDb_Old_id = unescape(
//           encodeURIComponent(translateData.transText + translateData.recive_lang)
//         )
//         let oldItem = {
//           id: btoa(indexDb_Old_id),
//           value: translateData.text
//         }
//         objectStore.add(oldItem)
//       }

//       db.close()
//     } catch (error) {}
//   }
// }

import * as idb from 'idb'

import { MD5 } from 'crypto-js'

export class MyIdb {
  databaseName = 'chatLogSel'
  databaseVersion = 1
  storeName = 'messages'
  db

  constructor() {
    this.init()
  }

  generateId(s) {
    return MD5(s).toString()
  }

  async getTranslate(translateData) {
    const store = this.db.transaction(this.storeName).store

    let indexDb_id = this.generateId(translateData.text + translateData.toLang)
    try {
      const result = await store.get(indexDb_id) // 获取指定ID的用户数据
      return result
    } catch (error) {
      console.warn(error)

      return
    }
  }

  async addTranslate(translateData) {
    const tx = this.db.transaction(this.storeName, 'readwrite');
    const store = tx.store;

    const newItem = {
      id: this.generateId(translateData.text + translateData.toLang),
      value: translateData.transText,
      toLang: translateData.toLang,
      type: "FORWARD"
    };

    try {
      if (newItem.value) {
        await store.put(newItem);
      }
    } catch (err) {
      console.warn('添加正向翻译失败:', err);
    }
    if (translateData.isInput && translateData.recive_lang && translateData.recive_lang !== translateData.toLang && translateData.transText !== translateData.text) {
      const oldItem = {
        id: this.generateId(translateData.transText + translateData.recive_lang),
        value: translateData.text,
        toLang: translateData.recive_lang,
        type: "REVERS"
      };

      try {
        if (oldItem.value) {
          await store.put(oldItem);
        }

      } catch (err) {
        console.warn('添加反向翻译失败:', err);
      }
    }

    tx.done.catch((e) => {
      console.error('事务失败:', e);
    });
  }


  async init() {
    try {
      this.db = await idb.openDB(this.databaseName, this.databaseVersion, {
        upgrade: (database) => {
          database.createObjectStore(this.storeName, {
            keyPath: 'id'
          })
        }
      })
    } catch (error) {
      idb
        .deleteDB(this.databaseName)
        .then(async (res) => {
          this.db = await idb.openDB(this.databaseName, this.databaseVersion, {
            upgrade: (database) => {
              database.createObjectStore(this.storeName, {
                keyPath: 'id'
              })
            }
          })
        })
        .catch((err) => {
          alert('初始化数据库失败')
        })
      console.warn('error', error)
    }
  }
}
// export { createIndexDB, getTranslateIndexDBDate, addTranslateIndexDb }


export const getPageIdbStoreValue = async (dataName, storeName, id) => {
  const db = await idb.openDB(dataName)
  return await db.transaction(storeName).store.get(id)
}