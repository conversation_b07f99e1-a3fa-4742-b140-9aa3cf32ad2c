import request from "../utils/request.js"
// 用户登录====================================================================================================
export function login(query) {
  return request({
    url: '/api/login/login',
    method: 'post',
    data: query,
    isLoading: false
  })
}
// 推出
export function logOut(query) {
  return request({
    url: '/api/login/logout',
    method: 'post',
    data: query,
    isLoading: false
  })
}
// 推出
export function getAppVersion() {
  return request({
    url: '/api/version/getLatestVersion',
    method: 'post',
    data: { system_type: window.platform.isWin ? 'windows' : "mac" },
    isLoading: false
  })
}
export function getAppUpdateHistoryVersion() {
  return request({
    url: '/api/version/getVersionHistory',
    method: 'post',
    data: { system_type: window.platform.isWin ? 'windows' : 'mac' },
    isLoading: false
  })
}
