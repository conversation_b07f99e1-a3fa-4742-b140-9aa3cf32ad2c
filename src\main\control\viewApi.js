import { ipcMain, session } from 'electron'
import { GlobalObject } from '@main/global/index.js'
import config from '../config'
import { getAiReplyApi } from "../service/repley"
import { queryParams } from "../index"
// 需要用到 crypto 模块 (Node.js环境)
import { createHash } from 'crypto';

export default class TabViewApi {
  createTabView() {
    ipcMain.handle('createTabView', (_, arg) => {
      GlobalObject.viewManager.addView(arg)
    })
  }
  showView() {
    ipcMain.on('showView', (_, arg) => {
      GlobalObject.viewManager.viewsMap.forEach((view) => {
        GlobalObject.mainWindow.contentView.removeChildView(view)
      })
      let view = GlobalObject.viewManager.getView(arg.name)
      if (view) {
        GlobalObject.mainWindow.contentView.addChildView(view)
        view.setBounds(GlobalObject.viewBounds)
      } else {
        GlobalObject.viewManager.addView(arg)
      }
    })
  }
  showWindow() {
    ipcMain.on('showWindow', (_) => {
      GlobalObject.viewManager.viewsMap.forEach((view) => {
        if (
          config.ai &&
          (view.itemInfo.platform === 'whatsapp' || view.itemInfo.platform === 'facebook')
        ) {
          view.setBounds({ x: 0, y: 0, width: 1, height: 1 })
        } else {
          GlobalObject.mainWindow.contentView.removeChildView(view)
        }
      })
      console.log(GlobalObject.mainWindow.contentView)
    })
  }
  setViewBounds() {
    ipcMain.handle('setViewBounds', (_, arg) => {
      console.log(arg)
      GlobalObject.viewBounds = arg
      if (GlobalObject.mainWindow?.contentView?.children) {
        GlobalObject.mainWindow.contentView.children.forEach((item) => {
          item.setBounds(GlobalObject.viewBounds)
        })
      }
      return true
    })
  }
  viewExecJavaScript() {
    ipcMain.handle('viewExecJavaScript', (_, { nowTabbarId: viewName, jsString }) => {
      try {
        return GlobalObject.viewManager.getView(viewName)?.webContents.executeJavaScript(jsString)
      } catch (error) {
        return false
      }
    })
  }
  getCurrentView() {
    ipcMain.handle('getCurrentView', () => {
      const children = GlobalObject.mainWindow.contentView.children
      if (children && children.length > 0) {
        let v = children[children.length - 1]
        return v.itemInfo || false
      }
      return false
    })
  }
  deleteView() {
    ipcMain.on('deleteView', (_event, itemIds) => {
      if (!itemIds || typeof itemIds !== 'string') return

      itemIds.split(',').forEach((id) => {
        if (!id.trim()) return

        try {
          let view = GlobalObject.viewManager.getView(id)
          if (view && view.itemInfo && view.itemInfo.session_id) {
            let s = session.fromPartition(`persist:account-${view.itemInfo.session_id}`)
            GlobalObject.mainWindow.contentView.removeChildView(view)
            GlobalObject.viewManager.deleteView(id)

            // 安全清理 session
            if (s && typeof s.clearStorageData === 'function') {
              s.clearStorageData().catch((err) => {
                console.warn('Failed to clear session data:', err)
              })
            }
          }
        } catch (error) {
          console.error(`Failed to delete view ${id}:`, error)
        }
      })
    })
  }
  initViewFunc() {
    ipcMain.handle('initViewFunc', (event) => {
      return new Promise((resolve) => {
        let execS = `; (function () {
  spoofNavigator();
  spoofCanvas();
  console.log(navigator);
  function spoofNavigator() {
    const overwrite = (obj, prop, value) => {
      Object.defineProperty(obj, prop, {
        get: () => value,
        configurable: true,
      });
    };
    overwrite(navigator, 'userAgentData', {
      brands: [
        { brand: "'Google Chrome'", version: "137" },
        { brand: "Chromium", version: "137" },
        { brand: "Not/A)Brand", version: "24" },


      ],
      mobile: false,
      platform: "Windows",
      toJSON: function () { return this },
      getHighEntropyValues: (hints) => {
        return Promise.resolve({
          platform: "Windows",
          platformVersion: "10.0.0",
          architecture: "x86",
          model: "",
          uaFullVersion: "*********"
        });
      }
    });
    overwrite(navigator, 'language', "zh-CN");
    overwrite(navigator, 'languages', ['zh-CN', "en", "en-US"]);
    overwrite(navigator, 'hardwareConcurrency', 4);
    overwrite(navigator, 'deviceMemory', 8);
    overwrite(navigator, 'vendor', "Google Inc.");
    overwrite(navigator, 'vendorSub', '');
    overwrite(navigator,"sayswho","Chrome 137");
  }
  function spoofCanvas() {
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function (...args) {
      try {
        const ctx = this.getContext('2d');
        injectCanvasNoise(ctx, this.width, this.height);
      } catch (e) { }
      return originalToDataURL.apply(this, args);
    };

    const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
    CanvasRenderingContext2D.prototype.getImageData = function (...args) {
      injectCanvasNoise(this, this.canvas.width, this.canvas.height);
      return originalGetImageData.apply(this, args);
    };

    function injectCanvasNoise(ctx, width, height) {
      if (!ctx || typeof ctx.fillText !== 'function') return;
      ctx.save();
      ctx.globalAlpha = 0.01;
      ctx.fillStyle = '#000';
      ctx.font = '16px Arial';
      ctx.fillText("canvas-noise-${Math.random() * 10}", 1, height - 1);
      ctx.restore();
    }
  }
  // 重写 history.pushState 方法
  const originalPushState = history.pushState
  history.pushState = function (...args) {
    const result = originalPushState.apply(this, args)
    window.dispatchEvent(new Event('pushstate'))
    return result
  }
  // 重写 history.replaceState 方法
  const originalReplaceState = history.replaceState
  history.replaceState = function (...args) {
    const result = originalReplaceState.apply(this, args)
    window.dispatchEvent(new Event('replacestate'))
    return result
  }
  return true
})();`
        event.sender
          .executeJavaScript(execS)
          .then((r) => {
            resolve(r) // Will be the JSON object from the fetch call
          })
          .catch((err) => {
            console.log(err)
            resolve(false)
          })
      })
    })
  }

  getViewSessionId() {
    ipcMain.handle('getViewSessionId', (_event) => {
      let viewId = _event.sender.id
      let session_id
      GlobalObject.viewManager.viewsMap.forEach((v) => {
        if (v.webContents.id === viewId) {
          session_id = v.itemInfo.session_id
        }
      })
      return session_id
    })
  }
  getAiReplyConfig() {
    ipcMain.handle('getAiReplyConfig', (_event) => {
      let o = { ai: config.ai, aiTimeRang: config.aiTimeRang }
      return o
    })
  }

  refreshView() {
    ipcMain.on('refreshView', (event, name) => {
      let v = GlobalObject.viewManager.getView(name)
      if (v) {
        v.webContents.reload()
      }
    })
  }

  sendMessageToView() {
    ipcMain.handle('sendMessageWindowToMain', (_event, params) => {
      const children = GlobalObject.mainWindow.contentView.children
      if (children && children.length > 0) {
        let v = children[children.length - 1]
        v.webContents.send('onMessageMainToView', params)
        return true
      }
      return false
    })
  }
  sendUserInfoMainToWindow() {
    ipcMain.on('sendUserInfoViewToMain', (_event, params) => {
      const { userId } = params
      let viewId = _event.sender.id
      GlobalObject.viewManager.viewsMap.forEach((v) => {
        if (v.webContents.id === viewId) {
          v.itemInfo.userId = userId
          GlobalObject.mainWindow.webContents.send('onGetUserInfo', { ...v.itemInfo, ...params })
        }
      })
    })
  }

  sendActiveFansIdMainToWindow() {
    ipcMain.on('sendActiveFansIdViewToMain', (_event, params) => {
      let viewId = _event.sender.id
      GlobalObject.viewManager.viewsMap.forEach((v) => {
        if (v.webContents.id === viewId) {
          GlobalObject.mainWindow.webContents.send('sendActiveFansId', { ...v.itemInfo, ...params })
        }
      })
    })
  }

  sendNewFansListMainToWindow() {
    ipcMain.on('addNewFansList', (_event, params) => {
      let viewId = _event.sender.id
      GlobalObject.viewManager.viewsMap.forEach((v) => {
        if (v.webContents.id === viewId) {
          GlobalObject.mainWindow.webContents.send('onAddNewFansList', { ...v.itemInfo, ...params })
        }
      })
    })
  }

  getSelectorInfo() {
    ipcMain.handle('getSelectorInfo', (_event, params) => {
      let { platform } = params
      return GlobalObject.selectorInfo[platform]
    })
  }

  batchStartView() {
    ipcMain.on('batchStartView', (_event, { batchStartViewList }) => {
      if (!Array.isArray(batchStartViewList)) return

      batchStartViewList.forEach((item) => {
        if (item && item.name) {
          GlobalObject.viewManager.addView(item)
        }
      })
    })
  }

  clickAt() {
    ipcMain.handle('clickAt', (event, { x, y }) => {
      event.sender.focus()
      event.sender.sendInputEvent({ type: 'mouseDown', x, y, button: 'left', clickCount: 1 })
      event.sender.sendInputEvent({ type: 'mouseUp', x, y, button: 'left', clickCount: 1 })
    })
  }

  aiAutoReply() {
    ipcMain.handle('aiAutoReply', async (_event, params) => {
      return await getAiReplyApi({
        question: params.question,
        dataset: queryParams.uuid,
        messageList: params.messageList,
        chat_id: params.chat_id
      })
    })
  }

  getWhatsappNameKey() {
    ipcMain.handle('getWhatsappNameKey', (e, userId) => {
      // 计算 MD5，得到 Buffer（16字节二进制）
      const md5Buffer = createHash('md5').update(`${userId}@c.us:last-pushname`, 'utf8').digest()

      // 将 Buffer 转成 Base64 字符串
      const b64 = md5Buffer.toString('base64')
      return b64
    })
  }
}

// const initViewHistory = function () {
//   // 重写 history.pushState 方法
//   const originalPushState = history.pushState
//   history.pushState = function (...args) {
//     const result = originalPushState.apply(this, args)
//     window.dispatchEvent(new Event('pushstate'))
//     return result
//   }

//   // 重写 history.replaceState 方法
//   const originalReplaceState = history.replaceState
//   history.replaceState = function (...args) {
//     const result = originalReplaceState.apply(this, args)
//     window.dispatchEvent(new Event('replacestate'))
//     return result
//   }

//   return true
// }
