<template>
	<div class="sys_base">
		<!-- 系统导航 -->
		<!-- <div :class="showHideWord?'sys_left':'sys_left sys_left_b'">
			<Tabar :softName='chatName' @changeChat='changeChat' @showHideWordFun='showHideWordFun'></Tabar>
		</div> -->
		<!-- 主要内容区 -->
		<div
			:class="tabarStore.showHideWord ? 'main_cont img_translate_main_cont' : 'main_cont img_translate_main_cont small_tab'">
			<!-- 图片翻译 -->
			<div :class="is_open ? 'img_translate_cont open_img_translate_cont' : 'img_translate_cont'">
				<div class="translate_img">
					<div class="tabs">
						<div :class="imgType == 1 ? 'item on' : 'item'" @click="changeImgType(1)">本地图片</div>
						<div :class="imgType == 2 ? 'item on' : 'item'" @click="changeImgType(2)">线上图片</div>
					</div>
					<el-form ref="form" :model="translateSet">
						<div class="lang_translage">

							<div class="select_info">

								<el-select v-model="translateSet.from_lang" placeholder="翻译前语言">
									<el-option v-for="item in allLang" :label="item.lang_name" :value="item.lang_code"></el-option>
								</el-select>
							</div>
							<div class="trans">
								<img :src="icon46" alt="">
							</div>
							<div class="select_info">

								<el-select v-model="translateSet.to_lang" placeholder="翻译后语言">
									<el-option v-for="item in allLang" :label="item.lang_name" :value="item.lang_code"></el-option>
								</el-select>
							</div>

						</div>
					</el-form>
					<div class="trans_cont" v-if="imgType == 1 && !imgTransSuccss">
						<!-- <div class="empty">
							<img :src="icon47" alt="">
							<p>单机或拖动图片到此区域进行上传</p>
							<span>仅支持单个图片上传，严禁上传被禁止的图片</span>
						</div> -->
						<el-upload class="upload-demo" drag name="picture" :show-file-list="false"
							:action="baseUrl + '/api/translate/uploads'" :headers="headers" :on-success="handleAvatarSuccess"
							:before-upload="beforeAvatarUpload">
							<img v-if="imageUrl" :src="baseUrl + imageUrl" alt="">
							<div class="empty" v-else>
								<img :src="icon47" alt="">
								<p>单机或拖动图片到此区域进行上传</p>
								<span>仅支持单个图片上传，严禁上传被禁止的图片</span>
							</div>

						</el-upload>
					</div>
					<div class="btn" v-if="imgType == 1 && !imgTransSuccss" @click="translateImgFun('img')">翻译</div>
					<div class="trans_web_img" v-if="imgType == 2 && !imgTransSuccss">
						<div class="img_url">
							<img :src="icon48" alt="">
							<p>请在下方输入图片链接开始翻译</p>
							<div class="input">
								<input type="text" v-model="translateUrl">
							</div>
							<div class="tip">支持png、jpeg、jpg、bmp格式，图片大小限制5M内，宽高不超过4000*4000</div>
							<div class="btn_web btn" @click="translateImgFun('url')">开始翻译</div>
						</div>
					</div>
					<!-- //翻译成功 -->
					<div class="trans_cont" v-if="imgTransSuccss">
						<div class="trnas_success">
							<div class="suc_img">
								<img :src="imgType == 1 ? baseUrl + imageUrl : transPreUrlImgBase" alt="">
							</div>
							<div class="cent_img">
								<img :src="icon49" alt="">
							</div>
							<div class="suc_img">
								<img :src="transOverImg" alt="" @click="downloadImage">
							</div>
						</div>
					</div>
					<div class="btn" v-if="imgTransSuccss" @click="returnTransImg">重新翻译</div>
				</div>
			</div>
			<!-- 用户设置 -->
			<div :class="is_open ? 'soft_set open_soft_set' : 'soft_set'">
				<UserSet ref="UserSetRef" @openSetPage='openSetPage' :softName='chatName'></UserSet>
			</div>
		</div>
	</div>

</template>

<script setup>
import CryptoJS from "crypto-js";
// import SoftSet from '../../components/SoftSet.vue'
import UserSet from '../../components/UserSet.vue'
import { useTabStore } from "@renderer/store/index.js"
const tabarStore = useTabStore()
import {
	ref, watch,
	onMounted,
	onUnmounted
} from 'vue'
// import {
// 	getWorkOrderSql
// } from "../../api/account.js"
import { useRouter, useRoute } from 'vue-router'  // 导入 useRouter
const router = useRouter()  // 获取路由实例
const route = useRoute();  // 获取当前路由对象
import {
	getAllLangSql,
	translateImgSql,
	getAllEngineSql
} from "../../api/account.js"
import { ElMessage, ElLoading } from 'element-plus'
// 导入本地图片
const icon46 = new URL('../../assets/img/icon46.svg', import.meta.url).href;
const icon47 = new URL('../../assets/img/icon47.svg', import.meta.url).href;
const icon48 = new URL('../../assets/img/icon48.svg', import.meta.url).href;
const icon49 = new URL('../../assets/img/icon49.svg', import.meta.url).href;
const img1 = new URL('../../assets/img/img1.png', import.meta.url).href;

const showHideWord = ref(true)
watch(
	() => route.query,
	(newParams, oldParams) => {
		// 设置左侧是否显示
		if (newParams.chat == 'slide') {
			showHideWord.value = newParams.type == 'true' ? true : false
		}
	},
	{ immediate: true } // 立即执行一次，以确保初始参数也被捕获
);

// 下载图片
const downloadImage = () => {

	// const link = document.createElement('a');
	// link.href = transOverImg.value;
	// link.download = 'image.jpg'; // 你可以根据需要更改文件名
	// document.body.appendChild(link);
	// link.click();
	// document.body.removeChild(link);
	let base64Data = transOverImg.value
	let fileName = 'image.jpg';
	const byteCharacters = atob(base64Data.split(',')[1]);
	const byteNumbers = new Array(byteCharacters.length);
	for (let i = 0; i < byteCharacters.length; i++) {
		byteNumbers[i] = byteCharacters.charCodeAt(i);
	}
	const byteArray = new Uint8Array(byteNumbers);
	const blob = new Blob([byteArray], { type: 'image/png' }); // 根据实际MIME类型修改
	const url = URL.createObjectURL(blob);
	const link = document.createElement('a');
	link.href = url;
	link.download = fileName;
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
	URL.revokeObjectURL(url); // 释放临时URL对象
}
// 上传图片设置
const baseUrl = ref(import.meta.env.VITE_BASE_API)
const currentTime = new Date(); // 创建一个Date对象，表示当前时间
const timestamp = Math.floor(currentTime.getTime() / 1000);
const token = localStorage.getItem('token') ? localStorage.getItem('token') : ''
const headers = ref({
	'token': token,
	'timestamp': timestamp
})
const imageUrl = ref()
const handleAvatarSuccess = (
	response,
	uploadFile
) => {
	if (response.code == 1) {
		imageUrl.value = response.data.path
	}
	// imageUrl.value = new URL(baseUrl.value + response.data.path, import.meta.url).href;
}

const beforeAvatarUpload = (rawFile) => {
	if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png' && rawFile.type !== 'image/jpg') {
		ElMessage.error('上传图片格式需为jpeg、png或jpg')
		return false
	} else if (rawFile.size / 1024 / 1024 > 2) {
		ElMessage.error('上传图片大小不能超过 2MB!')
		return false
	}
	return true
}
function sha256(message) {

	return CryptoJS.SHA256(message).toString();
}
const translateUrl = ref('')
const transOverImg = ref('')
const transPreUrlImgBase = ref('')
const translateImgFun = (type) => {
	let trans_info = '';
	if (type == 'img') {
		trans_info = imageUrl.value
	}
	if (type == 'url') {
		trans_info = translateUrl.value
	}

	if (trans_info == '') {
		ElMessage({
			showClose: true,
			message: '请上传或填写要翻译的图片内容',
			type: 'info'
		})
		return;
	}
	if (!translateSet.value.to_lang) {
		ElMessage({
			showClose: true,
			message: '请选择翻译后语言',
			type: 'info'
		})
		return;
	}
	let formData = {
		'type': type == 'img' ? 1 : 2,
		'path': trans_info,
		'fromLang': translateSet.value.from_lang ? translateSet.value.from_lang : 'auto',
		'toLang': translateSet.value.to_lang
	}
	const loading = ElLoading.service({
		lock: true,
		text: 'Loading',
		background: 'rgba(0, 0, 0, 0.7)',
	})
	translateImgSql(formData).then(response => {
		if (response.code == 1 && response.data.picture) {
			imgTransSuccss.value = true
			transOverImg.value = response.data.picture
			if (type == 'url') {
				transPreUrlImgBase.value = response.data.origin_picture
			}
		} else if (response.code == 400010) {
			ElMessage({
				showClose: true,
				message: response.msg,
				type: 'info'
			})
		} else {
			ElMessage({
				showClose: true,
				message: '图片翻译失败',
				type: 'info'
			})
		}
		loading.close()
	})
}
// 翻译设置数据
const translateSet = ref({})

// 设置翻译图片类型
const imgTransSuccss = ref(false)
const imgType = ref(1)
const changeImgType = (type) => {
	imgType.value = type
	imgTransSuccss.value = false
}
// 重新翻译
const returnTransImg = () => {
	imgTransSuccss.value = false
}
// 支持平台列表
const chatName = ref()
onMounted(() => {
	chatName.value = route.query.chat
})
// 改变平台
const changeChat = (cN) => {
	chatName.value = cN
}
// 设置是否显示设置信息页面
const is_open = ref(false)
const openSetPage = (son_b) => {
	is_open.value = son_b
}


// 获取所有语言
const allLang = ref({})

function getAllLang() {
	getAllLangSql({}).then(response => {
		if (response.code == 1) {
			allLang.value = response.data
		}
	})
}
getAllLang()

// 获取所有翻译引擎
const allEngine = ref({})

function getAllEngine() {
	getAllEngineSql({}).then(response => {
		if (response.code == 1) {
			allEngine.value = response.data
		}
	})
}
getAllEngine()
</script>

<style lang="scss">
@import url("../../assets/style/img_translate.scss");
</style>