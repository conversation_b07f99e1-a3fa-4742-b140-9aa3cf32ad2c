import { app, ipcMain } from 'electron'
import { GlobalObject } from '../global'
import config from '../config'
import { platform } from '@electron-toolkit/utils'

export class MessageManager {
  count = 0
  messageMap = new Proxy(
    {},
    {
      set: (target, field, value) => {
        target[field] = value
        this.watchMessage(target)
        return true
      }
    }
  )

  getCount() {
    return this.count
  }
  setMessageCount(params) {
    const { platform, unReadCount } = params
    this.messageMap[platform] = { unReadCount }
  }
  watchMessage(target) {
    let c = Object.values(this.messageMap)
      .filter(item => item && typeof item.unReadCount === 'number')
      .map((item) => item.unReadCount)
      .reduce((pre, cur) => {
        return pre + cur
      }, 0)

    this.count = c
    if (config.isShowMessage) {
      this.showMessageCount()
    } else {
      this.hideMessageCount()
    }
    GlobalObject.mainWindow?.webContents.send('unReadMessageCountChange', target)
  }

  showMessageCount() {
    try {
      if (platform.isMacOS && typeof app.setBadgeCount === 'function') {
        app.setBadgeCount(this.count)
      }
    } catch (error) {
      console.error('Failed to show message count:', error)
    }
  }

  hideMessageCount() {
    try {
      if (platform.isMacOS) {
        app.setBadgeCount(0)
      }
    } catch (error) {
      console.error('Failed to hide message count:', error)
    }
  }
}
