// googleAsyncTranslator.js

export const googleEngineMap = new Map([
  ["googlexjp", "https://www.google.com.sg"],
  ["googlehk", "https://www.google.com.hk"],
  ["googlehw", "https://www.google.com"],
]);

/**
 * 调用 Google /async/translate 接口获取翻译结果
 * @param {Object} params
 * @param {string} params.text        要翻译的文本
 * @param {string} params.fromLang    源语言（如 "auto"）
 * @param {string} params.toLang      目标语言（如 "en"）
 * @param {string} params.engineCode  Google 域名代码，如 "googlehk"
 * @returns {Promise<string|null>}    翻译文本或 null
 */
export async function googleAsyncTranslate({ text, fromLang, toLang, engineCode = 'googlehk' }) {
  const baseURL = googleEngineMap.get(engineCode);
  if (!baseURL) {
    console.error(`无效的 engineCode: ${engineCode}`);
    return null;
  }

  const id = Date.now();
  const formData = new URLSearchParams({
    async: `translate,sl:${fromLang},tl:${toLang},st:"${encodeURIComponent(text)}",id:${id},qc:true,ac:true,_id:tw-async-translate,_pms:s,_fmt:pc`,
  });

  try {
    const response = await fetch(`${baseURL}/async/translate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
      body: formData.toString(),
    });

    const html = await response.text();

    // 简单提取翻译结果（通常在第一个 <div> 中）
    const match = html.match(/<div[^>]*>(.*?)<\/div>/);
    const translated = match ? match[1].replace(/<[^>]+>/g, '') : null;

    return {
      code: 1,
      data: {
        result: translated
      }
    };
  } catch (err) {
    console.error('翻译请求失败:', err);
    return null;
  }
}
