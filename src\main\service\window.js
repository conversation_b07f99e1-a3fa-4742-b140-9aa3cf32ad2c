import { app, shell, BrowserWindow, ipc<PERSON>ain, Tray, Menu, dialog, session, nativeImage } from 'electron'
import { autoUpdater } from 'electron-updater'
import { onMainBwReisze } from '@main/service/viewManager.js'
import { is, platform } from '@electron-toolkit/utils'
import { GlobalObject } from '@main/global/index.js'
import icon from '@main/../../resources/icon.png?asset'
import { getUs } from "@main/utils/index.js"
import path from 'path'
let tray = null // 确保tray变量在外部作用域中，防止被自动删除
export function createWindow() {
  // Create the browser window.
  let w = new BrowserWindow({
    width: 1452,
    height: 800,
    minWidth: 1100,
    minHeight: 660,
    show: false,
    autoHideMenuBar: true,
    frame: false,
    icon,
    transparent: true,
    webPreferences: {
      preload: path.join(__dirname, '../preload/index.js'),
      sandbox: false,
      contextIsolation: true,
      userAgent: getUs(platform.isMacOS),
      // webviewTag: true
    }
  })
  onMainBwReisze(w)
  session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
    if (permission === 'media') {
      callback(true); // 或结合逻辑缓存域名
    } else {
      callback(false);
    }
  });

  w.on('ready-to-show', () => {
    if (process.platform === 'win32') {
      w.hookWindowMessage(278, () => {
        w.setEnabled(false)
        setTimeout(() => {
          w.setEnabled(true)
        })
        return true
      })
    }
    w.show()
  })

  w.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return {
      action: 'deny'
    }
  })
  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    w.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    console.log(path.join(__dirname, '../renderer/index.html'))

    w.loadFile(path.join(__dirname, '../renderer/index.html'))
  }
  // 金婷版本
  ipcMain.on('getVesion', (event, arg) => {
    let v = app.getVersion()
    event.reply('sendVersion', v)
  })
  ipcMain.handle('onUserLoginOut', async (e, p) => {
    await dialog.showMessageBox(GlobalObject.mainWindow, {
      type: 'question',
      buttons: ['确定'],
      defaultId: 0,
      title: '退出提醒',
      message: p
    })
    return true
  })
  // 监听view获取用户的信息
  ipcMain.on('sendToMainUserInfo', (_event, ...args) => {
    console.log(GlobalObject.viewManager.viewsMap)
    let viewId = _event.sender.id
    let session_id
    GlobalObject.viewManager.viewsMap.forEach((v) => {
      if (v.webContents.id === viewId) {
        session_id = v.itemInfo.session_id
      }
    })
    w.webContents.send('getUserInfo', ...args, session_id)
  })

  // 创建托盘图标

  try {
    const iconPath = platform.isMacOS
      ? path.join(__dirname, '../../build/icon.icns')  // 修正路径
      : path.join(__dirname, '../../icon.png')   // Windows/Linux 使用 .png

    const trayIcon = nativeImage.createFromPath(iconPath)

    if (trayIcon.isEmpty()) {
      console.warn('Tray icon not found, skipping tray creation')
    }

    tray = new Tray(trayIcon.resize({ width: 20, height: 20, quality: "best" }))
    app.on('quit', () => {
      tray.destroy()
    })
    // 设置托盘图标的提示文本
    tray.setToolTip('蓝海译通')

    // 创建托盘图标的上下文菜单
    const contextMenu = Menu.buildFromTemplate([
      {
        label: '打开',
        click: () => {
          w.show() // 点击托盘图标时显示主窗口
        }
      },
      {
        label: '退出',
        click: () => {
          app.quit() // 点击退出菜单时退出应用
        }
      }
    ])

    // 设置托盘图标的上下文菜单
    tray.setContextMenu(contextMenu)

    if (platform.isMacOS) {
      platform.isMacOS
    } else {
      tray.on('click', () => {
        w.isVisible() ? w.focus() : w.show()
      })
    }

  } catch (error) {
    console.error('Failed to create tray:', error)
  }

  // 热更新
  let is_hand_btn = false

  if (process.platform !== 'darwin') {
    autoUpdater.autoDownload = false
    let downloadUrl = `${import.meta.env.VITE_BASE_API}/update`
    console.log('downloadUrl:', downloadUrl)

    autoUpdater.setFeedURL(downloadUrl)
    // autoUpdater.setFeedURL('https://lhytapi.blueglob.com/update')
    // 检查是否有更新
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...')
    })
    // 更新版本信息
    autoUpdater.on('update-available', (info) => {
      console.log('Update available:')
      console.log(info)
      try {
        w.webContents.send('update-available', info.version)
      } catch (error) {
        console.log(error)
      }
    })
    // 无有效版本
    autoUpdater.on('update-not-available', (info) => {
      console.log('No update available:', info)
      try {
        w.webContents.send('no-update-version', is_hand_btn)
      } catch (error) {
        console.log(error)
      }
    })
    // 下载报错
    autoUpdater.on('error', (err) => {
      console.error('Update error:', err)
      dialog.showMessageBox({
        type: 'warning',
        title: '更新出错，请稍后重试',
        message: '是否要关闭窗口？'
      })
    })
    // 下载进度
    autoUpdater.on('download-progress', (progressObj) => {
      // let logMessage =
      // 	`Download speed: ${progressObj.speed} - Downloaded ${progressObj.percent}% (${progressObj.transferred}/${progressObj.total})`;
      // console.log(logMessage);
      w.webContents.send(
        'download-progress-number',
        progressObj.percent,
        JSON.stringify(progressObj)
      )
    })
    // 下载完成
    autoUpdater.on('update-downloaded', (info) => {
      console.log('Update downloaded:', info)
      autoUpdater.quitAndInstall()
    })
    autoUpdater
      .checkForUpdates()
      .then((info) => {
        if (info.updateInfo) {
          console.log('new version:', info.updateInfo)
          // 发现有新版本，可以提示用户是否下载更新
        } else {
          console.log('当前已是最新版本')
          // 没有新版本，可以提示用户当前已是最新版本
        }
      })
      .catch((error) => {
        // console.log('update error:', error)
      })
    // 从渲染进程接收检查更新的请求
    ipcMain.on('check-for-update', () => {
      console.log('kkkk')
      is_hand_btn = true
      autoUpdater
        .checkForUpdates()
        .then((info) => {
          if (info.updateInfo) {
            console.log('发现新版本:', info.updateInfo)
            // 发现有新版本，可以提示用户是否下载更新
          } else {
            console.log('当前已是最新版本')
            // 没有新版本，可以提示用户当前已是最新版本
          }
        })
        .catch((error) => {
          console.error('检查更新失败:', error)
        })
      // return autoUpdater.checkForUpdates();
    })
    ipcMain.on('now_down_exe', () => {
      autoUpdater.downloadUpdate()
    })
    // 处理更新下载完成的消息
    ipcMain.on('quit-and-install', () => {
      autoUpdater.quitAndInstall()
    })
  }
  // 接收主进程传来的数据 处理更新打开聊天工具列表
  ipcMain.on('updateOpenChatList', (event) => {
    w.webContents.send('app-update-chat-list')
  })
  return w
}
