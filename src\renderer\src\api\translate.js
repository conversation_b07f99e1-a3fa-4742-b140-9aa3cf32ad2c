import request from "../utils/request.js"
// import { googleAsyncTranslate } from "../utils/googleTranslateApi.js"

// 翻译接口====================================================================================================
export async function translate(query) {

  const result = await window.electron.ipcRenderer.invoke("googleAsyncTranslate", query)
  if (result) {
    return result;
  }
  return request({
    url: '/api/translate/translate',
    method: 'post',
    data: query
  })
}
