// src/router/index.js
import { createRouter, createWebHashHistory } from 'vue-router'

// 定义路由
const routes = [
  {
    path: '/home',
    name: 'home'
  },
  // 添加更多的路由...
  {
    path: '/chat',
    name: 'chat',
    component: () => import('../views/chatTool/index.vue')
  },
  {
    path: '/',
    name: 'index',
    component: () => import('../views/index/index.vue')
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/login/index.vue')
  },
  {
    path: '/account',
    name: 'account',
    component: () => import('../views/coustome/account.vue')
  },
  {
    path: '/img_translate',
    name: 'img_translate',
    component: () => import('../views/coustome/img_translate.vue')
  },
  {
    path: '/coustome',
    name: 'coustome',
    component: () => import('../views/coustome/coustome_list.vue')
  },
  {
    path: '/quick',
    name: 'quick',
    component: () => import('../views/set/quick.vue')
  },
  {
    path: '/all_translate',
    name: 'all_translate',
    component: () => import('../views/set/all_translate.vue')
  },
  {
    path: '/sys_set',
    name: 'sys_set',
    component: () => import('../views/set/sys_set.vue')
  },
  {
    path: '/contact',
    name: 'contact',
    component: () => import('../views/set/contact.vue')
  },
  {
    path: '/script',
    name: 'script',
    component: () => import('../views/set/script.vue')
  },
  {
    path: '/state',
    name: 'state',
    component: () => import('../views/set/state.vue')
  }
]

// 创建router实例
const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes
})

router.beforeEach((to, from) => {
  // 验证是否登录
  let token = localStorage.getItem('token')
  let realName = localStorage.getItem('realname')
  if (token && realName) {
    if (to.path !== '/chat') {
      console.log("to", to);
      window.electron.ipcRenderer.send('showWindow')
    }
  } else {
    window.electron.ipcRenderer.send('showWindow')
    if (to.path !== '/login') {
      return {
        path: '/login'
      }
    }
  }
})

export default router
